using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class ClientService
{
    private readonly ApplicationDbContext _context;

    public ClientService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<ClientDto>> GetClientsAsync()
    {
        return await _context.Clients
            .Select(c => new ClientDto
            {
                ClientId = c.ClientId,
                ClientName = c.ClientName,
                ContactInfo = c.ContactInfo,
                IsActive = c.IsActive,
                
            })
            .ToListAsync();
    }

    public async Task<ClientDto?> GetClientByIdAsync(int id)
    {
        var client = await _context.Clients.FindAsync(id);
        if (client == null)
        {
            return null;
        }

        return new ClientDto
        {
            ClientId = client.ClientId,
            ClientName = client.ClientName,
            ContactInfo = client.ContactInfo,
            IsActive = client.IsActive,
            
        };
    }

    public async Task<Client> CreateClientAsync(ClientDto clientDto)
    {
        var client = new Client
        {
            ClientName = clientDto.ClientName,
            ContactInfo = clientDto.ContactInfo,
            IsActive = clientDto.IsActive,
            CreatedDate = DateTime.Now
        };

        _context.Clients.Add(client);
        await _context.SaveChangesAsync();
        return client;
    }

    public async Task UpdateClientAsync(ClientDto clientDto)
    {
        var client = await _context.Clients.FindAsync(clientDto.ClientId);
        if (client != null)
        {
            client.ClientName = clientDto.ClientName;
            client.ContactInfo = clientDto.ContactInfo;
            client.IsActive = clientDto.IsActive;
            await _context.SaveChangesAsync();
        }
    }

    public async Task DeleteClientAsync(int id)
    {
        var client = await _context.Clients.FindAsync(id);
        if (client != null)
        {
            _context.Clients.Remove(client);
            await _context.SaveChangesAsync();
        }
    }
}