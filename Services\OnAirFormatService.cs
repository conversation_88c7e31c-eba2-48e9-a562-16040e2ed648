using GELProgramsInventory.Models;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class OnAirFormatService
{
    private readonly ApplicationDbContext _context;

    public OnAirFormatService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<OnAirFormat>> GetOnAirFormatsAsync()
    {
        return await _context.OnAirFormats.ToListAsync();
    }
}
