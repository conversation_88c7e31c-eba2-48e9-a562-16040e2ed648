using GELProgramsInventory.Models;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class GenreService
{
    private readonly ApplicationDbContext _context;

    public GenreService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Genre>> GetGenresAsync()
    {
        return await _context.Genres.ToListAsync();
    }
}
