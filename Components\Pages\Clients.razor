@page "/clients"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@inject ClientService ClientService
@rendermode InteractiveServer

<h1>Clients</h1>

<SfButton OnClick="@OpenCreateDialog">Create New Client</SfButton>
<SfDialog @ref="dlgClient" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="600px" Visible="false">
    <DialogTemplates>
        <Header>Client Information</Header>
        <Content>
            <EditForm Model="_selectedClient" OnValidSubmit="SaveClient" FormName="ClientForm">
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Client Name" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedClient.ClientName"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfTextBox Placeholder="Contact Info" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedClient.ContactInfo"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md" style="display: flex; align-items: center; gap: 5px;" >
                        <span><b>Status</b></span> <SfSwitch @bind-Checked="_selectedClient.IsActive"></SfSwitch> <span> @_selectedClient.Status</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfButton CssClass="e-primary">Save</SfButton>
                        
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfGrid @ref="_grid" DataSource="@_clientsList" AllowPaging="true">
    <GridColumns>
        
        <GridColumn Field="@nameof(ClientDto.ClientName)" HeaderText="Client Name" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ClientDto.ContactInfo)" HeaderText="Contact Info" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ClientDto.Status)" HeaderText="Status" AutoFit="true" ></GridColumn>
        
        <GridColumn HeaderText="Actions" Width="150">
            <Template Context="clientContext">
                <SfButton OnClick="@(() => OpenEditDialog((ClientDto)clientContext))">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteClient(((ClientDto)clientContext).ClientId))">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private List<ClientDto> _clientsList = new();
    private SfGrid<ClientDto> _grid = new();
    private ClientDto _selectedClient = new();
    private SfDialog? dlgClient;
    private List<Breadcrumb.BreadcrumbItem> _breadcrumbItems = new List<Breadcrumb.BreadcrumbItem>
    {
        new Breadcrumb.BreadcrumbItem { Text = "Home", Href = "/" },
        new Breadcrumb.BreadcrumbItem { Text = "Clients", Href = "/clients", IsActive = true }
    };
    protected override async Task OnInitializedAsync()
    {
        _clientsList = await ClientService.GetClientsAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedClient = new ClientDto() {IsActive = true};
        await dlgClient.ShowAsync();
    }

    private async Task OpenEditDialog(ClientDto client)
    {
        _selectedClient = client;
        await dlgClient.ShowAsync();
    }

    private async Task SaveClient()
    {
        if (_selectedClient.ClientId == 0)
        {
            await ClientService.CreateClientAsync(_selectedClient);
        }
        else
        {
            await ClientService.UpdateClientAsync(_selectedClient);
        }

        _clientsList = await ClientService.GetClientsAsync();
        await _grid.Refresh();
        await dlgClient.HideAsync();
    }

    private async Task DeleteClient(int clientId)
    {
        await ClientService.DeleteClientAsync(clientId);
        _clientsList = await ClientService.GetClientsAsync();
        _grid.Refresh();
    }

}