<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        @foreach (var item in Items)
        {
            <li class="breadcrumb-item @(item.IsActive ? "active" : "")">
                @if (item.IsActive)
                {
                    @item.Text
                }
                else
                {
                    <a href="@item.Href">@item.Text</a>
                }
            </li>
        }
    </ol>
</nav>

@code {
    [Parameter]
    public List<BreadcrumbItem> Items { get; set; } = new List<BreadcrumbItem>();

    public class BreadcrumbItem
    {
        public string Text { get; set; }
        public string Href { get; set; }
        public bool IsActive { get; set; }
    }
}