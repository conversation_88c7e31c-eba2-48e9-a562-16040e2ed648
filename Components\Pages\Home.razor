﻿@page "/"
@using Microsoft.FluentUI.AspNetCore.Components.Icons
@inject NavigationManager NavMgr
<PageTitle>GEL Programs Inventory - Dashboard</PageTitle>

<div class="dashboard-container">
    <div class="header-section">
        <h1 class="dashboard-title">
            <FluentIcon Icon="Icons.Regular.Size24.Home" />
            GEL Programs Inventory Dashboard
        </h1>
        <p class="dashboard-subtitle">Manage your television programs, clients, and production data</p>
    </div>

    @*<div class="stats-section">
        <div class="stats-grid">
            <div class="stat-card primary">
                <FluentIcon Icon="Icons.Regular.Size24.Video" />
                <div class="stat-content">
                    <h3>Programs</h3>
                    <p>Manage TV Programs</p>
                </div>
            </div>
            <div class="stat-card secondary">
                <FluentIcon Icon="Icons.Regular.Size24.People" />
                <div class="stat-content">
                    <h3>Clients</h3>
                    <p>Client Management</p>
                </div>
            </div>
            <div class="stat-card success">
                <FluentIcon Icon="Icons.Regular.Size24.Building" />
                <div class="stat-content">
                    <h3>Production Houses</h3>
                    <p>Production Partners</p>
                </div>
            </div>
            <div class="stat-card warning">
                <FluentIcon Icon="Icons.Regular.Size24.Apps" />
                <div class="stat-content">
                    <h3>Genres</h3>
                    <p>Content Categories</p>
                </div>
            </div>
        </div>
    </div>*@

    <div class="entities-section">
        @*<h2 class="section-title">Entity Management</h2>*@
        <div class="entities-grid">
            <!-- Programs Card -->
            <div class="entity-card" style="cursor:pointer" @onclick="@(()=>Navigate("/programs"))">
                <div class="card-header programs">
                    <FluentIcon Icon="Icons.Regular.Size24.Video" />
                    <h3>Programs</h3>
                </div>
                <div class="card-body">
                    <p>Manage television programs, episodes, and content inventory</p>
                    <div class="card-actions">
                        
                    </div>
                </div>
            </div>

            <!-- Clients Card -->
            <div class="entity-card" style="cursor:pointer" @onclick="@(()=>Navigate("/clients"))">
                <div class="card-header clients">
                    <FluentIcon Icon="Icons.Regular.Size24.People" />
                    <h3>Clients</h3>
                </div>
                <div class="card-body">
                    <p>Manage client information and contact details</p>
                    <div class="card-actions">
                        
                    </div>
                </div>
            </div>

            <!-- Production Houses Card -->
            <div class="entity-card" style="cursor:pointer" @onclick="@(()=>Navigate("/production-houses"))">
                <div class="card-header production">
                    <FluentIcon Icon="Icons.Regular.Size24.Building" />
                    <h3>Production Houses</h3>
                </div>
                <div class="card-body">
                    <p>Manage production companies and their contact information</p>
                    <div class="card-actions">
                        
                    </div>
                </div>
            </div>

            <!-- Genres Card -->
            <div class="entity-card" style="cursor:pointer" @onclick="@(()=>Navigate("/genres"))">
                <div class="card-header genres">
                    <FluentIcon Icon="Icons.Regular.Size24.Apps" />
                    <h3>Genres</h3>
                </div>
                <div class="card-body">
                    <p>Manage content genres and categories</p>
                    <div class="card-actions">
                        
                    </div>
                </div>
            </div>

            <!-- On Air Formats Card -->
            <div class="entity-card" style="cursor:pointer" @onclick="@(()=>Navigate("/on-air-types"))">
                <div class="card-header formats">
                    <FluentIcon Icon="Icons.Regular.Size24.Tv" />
                    <h3>On Air Formats</h3>
                </div>
                <div class="card-body">
                    <p>Manage broadcasting formats and specifications</p>
                    <div class="card-actions">
                        
                    </div>
                </div>
            </div>

            <!-- Syndication Formats Card -->
            <div class="entity-card" style="cursor:pointer" @onclick="@(()=>Navigate("/syndication-formats"))">
                <div class="card-header syndication">
                    <FluentIcon Icon="Icons.Regular.Size24.Share" />
                    <h3>Syndication Formats</h3>
                </div>
                <div class="card-body">
                    <p>Manage syndication formats and distribution methods</p>
                    <div class="card-actions">
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="quick-actions-section">
        <h2 class="section-title">Quick Actions</h2>
        <div class="quick-actions">
            <SfButton CssClass="e-info e-large" >
                <FluentIcon Icon="Icons.Regular.Size24.DataHistogram" />
                View Reports
            </SfButton>
            <SfButton CssClass="e-warning e-large" >
                <FluentIcon Icon="Icons.Regular.Size24.Search" />
                Advanced Search
            </SfButton>
            <SfButton CssClass="e-secondary e-large" >
                <FluentIcon Icon="Icons.Regular.Size24.Settings" />
                System Settings
            </SfButton>
        </div>
    </div>
</div>

@code {

}

<style>
    .dashboard-container {
        padding: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .header-section {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .dashboard-title {
        font-size: 2.5rem;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
    }

    .dashboard-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0;
    }

    .stats-section {
        margin-bottom: 40px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .stat-card {
        padding: 25px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-card.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
    .stat-card.secondary { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
    .stat-card.success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; }
    .stat-card.warning { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; }

    .stat-content h3 {
        margin: 0 0 5px 0;
        font-size: 1.4rem;
    }

    .stat-content p {
        margin: 0;
        opacity: 0.9;
    }

    .section-title {
        font-size: 2rem;
        margin-bottom: 25px;
        color: #333;
        border-bottom: 3px solid #667eea;
        padding-bottom: 10px;
    }

    .entities-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .entity-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .entity-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        padding: 25px;
        color: white;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .card-header.programs { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .card-header.clients { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .card-header.production { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .card-header.genres { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    .card-header.formats { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
    .card-header.syndication { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; }

    .card-header h3 {
        margin: 0;
        font-size: 1.5rem;
    }

    .card-body {
        padding: 25px;
    }

    .card-body p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .card-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .quick-actions-section {
        text-align: center;
        padding: 30px;
        background: #f8f9fa;
        border-radius: 15px;
    }

    .quick-actions {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .entities-section {
        margin-bottom: 40px;
    }

    @@media (max-width: 768px) {
        .dashboard-title {
            font-size: 2rem;
            flex-direction: column;
            gap: 10px;
        }

        .entities-grid {
            grid-template-columns: 1fr;
        }

        .quick-actions {
            flex-direction: column;
            align-items: center;
        }

        .card-actions {
            justify-content: center;
        }
    }
</style>

@code {
    private void Navigate(string link)   
    {
        NavMgr.NavigateTo(link);
    }
}