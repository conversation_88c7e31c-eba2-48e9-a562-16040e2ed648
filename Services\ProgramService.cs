using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class ProgramService
{
    private readonly ApplicationDbContext _context;

    public ProgramService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<ProgramDto>> GetProgramsAsync()
    {
        return await _context.Programs
            .Include(p => p.Client)
            .Include(p => p.Genre)
            .Include(p => p.ProductionHouse)
            .Include(p => p.OnAirFormat)
            .Include(p => p.SyndicationFormat)
            .Select(p => new ProgramDto
            {
                ProgramId = p.ProgramId,
                ProgramName = p.ProgramName,
                ClientId = p.ClientId,
                ClientName = p.Client.ClientName,
                GenreId = p.GenreId,
                GenreName = p.Genre.GenreName,
                ProductionHouseId = p.ProductionHouseId,
                ProductionHouseName = p.ProductionHouse.ProductionHouseName,
                OnAirFormatId = p.OnAirFormatId,
                OnAirFormatName = p.OnAirFormat.FormatName,
                SyndicationFormatId = p.SyndicationFormatId,
                SyndicationFormatName = p.SyndicationFormat.FormatName,
                Remark = p.Remark,
                DriveSerialNumber = p.DriveSerialNumber,
                IsSyndication = p.IsSyndication,
                YearOfLaunch = p.YearOfLaunch,
                IsActive = p.IsActive
            })
            .ToListAsync();
    }

    public async Task<ProgramDto?> GetProgramByIdAsync(int id)
    {
        var program = await _context.Programs
            .Include(p => p.Client)
            .Include(p => p.Genre)
            .Include(p => p.ProductionHouse)
            .Include(p => p.OnAirFormat)
            .Include(p => p.SyndicationFormat)
            .FirstOrDefaultAsync(p => p.ProgramId == id);

        if (program == null)
        {
            return null;
        }

        return new ProgramDto
        {
            ProgramId = program.ProgramId,
            ProgramName = program.ProgramName,
            ClientId = program.ClientId,
            ClientName = program.Client?.ClientName,
            GenreId = program.GenreId,
            GenreName = program.Genre?.GenreName,
            ProductionHouseId = program.ProductionHouseId,
            ProductionHouseName = program.ProductionHouse?.ProductionHouseName,
            OnAirFormatId = program.OnAirFormatId,
            OnAirFormatName = program.OnAirFormat?.FormatName,
            SyndicationFormatId = program.SyndicationFormatId,
            SyndicationFormatName = program.SyndicationFormat?.FormatName,
            Remark = program.Remark,
            DriveSerialNumber = program.DriveSerialNumber,
            IsSyndication = program.IsSyndication,
            YearOfLaunch = program.YearOfLaunch,
            IsActive = program.IsActive
        };
    }

    public async Task<GELProgramsInventory.Models.Program> CreateProgramAsync(ProgramDto programDto)
    {
        var program = new GELProgramsInventory.Models.Program
        {
            ProgramName = programDto.ProgramName,
            ClientId = programDto.ClientId,
            GenreId = programDto.GenreId,
            ProductionHouseId = programDto.ProductionHouseId,
            OnAirFormatId = programDto.OnAirFormatId,
            SyndicationFormatId = programDto.SyndicationFormatId,
            Remark = programDto.Remark,
            DriveSerialNumber = programDto.DriveSerialNumber,
            IsSyndication = programDto.IsSyndication,
            YearOfLaunch = programDto.YearOfLaunch,
            IsActive = programDto.IsActive,
            CreatedDate = DateTime.Now
        };

        _context.Programs.Add(program);
        await _context.SaveChangesAsync();
        return program;
    }

    public async Task UpdateProgramAsync(ProgramDto programDto)
    {
        var program = await _context.Programs.FindAsync(programDto.ProgramId);
        if (program != null)
        {
            program.ProgramName = programDto.ProgramName;
            program.ClientId = programDto.ClientId;
            program.GenreId = programDto.GenreId;
            program.ProductionHouseId = programDto.ProductionHouseId;
            program.OnAirFormatId = programDto.OnAirFormatId;
            program.SyndicationFormatId = programDto.SyndicationFormatId;
            program.Remark = programDto.Remark;
            program.DriveSerialNumber = programDto.DriveSerialNumber;
            program.IsSyndication = programDto.IsSyndication;
            program.YearOfLaunch = programDto.YearOfLaunch;
            program.IsActive = programDto.IsActive;
            await _context.SaveChangesAsync();
        }
    }

    public async Task DeleteProgramAsync(int id)
    {
        var program = await _context.Programs.FindAsync(id);
        if (program != null)
        {
            _context.Programs.Remove(program);
            await _context.SaveChangesAsync();
        }
    }
}