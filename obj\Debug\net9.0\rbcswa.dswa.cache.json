{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["RLPqTxFobI7saIdeFf1qWpFnz7qUf2ZW++CauefmwUU=", "FH1AtHtTIDHa2vkx4XdlBiNzTuem4qhNfYuYTYUTWtw=", "SurCd6fKPR4iHqTD06ICfi56XFBx0jKOTw+ICFkc3sI=", "iUfhl8Y8Al0DX8Pej3v76vvRE5x+9cg2aA+OEFYoMBA=", "25R8uL6d+7aoKlAF82EAWqj72f/byChFMqa0ad5fPXQ=", "ES2P44WCF6Zc38qva7DBQ7wHwdKhbOM72yitNlfOD4w=", "BQ/Bh9x2bxNqV2UUHKHPhGyN346cAoU8i1d+sZPvCbc=", "q0PhSQCVgJks7xw+p/sWR3p4G8uH2TunYkztZ4jvp9g=", "h0gxOf8vM6EICUVednlCWEpswCh68LAKFVDFVw1vMMo=", "6/AvJOdiMQeB7ya0BlnqwFuytQA3UUrOVQNklkOsPe8=", "fhPOMyAThTK25Bos74kTxD2IFpBR7OMVVp482yhDzVo=", "6UxDLp1pcbQXtmxt/Xecql0pKm7SnSJhaJvISkQMi+Y=", "NxdCmeB8ZrffIPs2u6o7AFaQ4nYTotpN21hQ5P6EgL4=", "x4UiC9Shwu8gwrFqd8HQr/KNyWRHpzm0SLiFvFFMrt0=", "rczMxIFtaHeZnCk5qOmY0nE35qov1QG2LUbJ7mXo9oU=", "HUXdU5BpBJthak9nu3jz/WRPupPd28WLXwFu6fWTkSk=", "MtMY2X0vO+Azy54KVym/h3hZw8O7jzl9Lw6FvTtoxpI=", "cpBYQvbYkJD262Y+79gEssqkgCj+oqLOmXzZ/M2dMVk=", "ZqhKd1jjyqGTWPBUOYa8vIxcl9SwdRn4ezAIfO2FXX4=", "FSy3lgvFi5MxwlGZ2lRCd9cBbuHA7g3cDe/IRlLC2JU=", "2n/jnzPRTGipnJxf/gAIgnVBsSqLiUcfFgKwEYWI8BU=", "6BeXDQCXAv+5/8G+IA0SiMo5jWOLRRPlS6dZIOBGkpI=", "qzYw23WpATn29yCai0IFfwJ/tGDqt3lvprTz0nyCiw0=", "fOzIU/WX53sBvtnl72C+21Pzhz/WhmnB2E/CW5Tyyow=", "ENyJnoaloYZRBrEElBJTJFeU6SR1QEaft8LyADXLbhY=", "Q3Py2w+10Pv7wE3FMtENQ2e4lovzzYK9CrLIs4Z5PlE=", "MriJojb+CZR8k5KMkaRy2udd66BpA4kdZl2ZwwEHaVA=", "5RnQVRqq5c0La9xXUoqdQfUZnrizICmLmv4AQPNjXU0=", "s+Iaalg4nOIWUk8+UJpXmfDS42Nf4TQ2Sgyr4RLxizI=", "x5k9gFR/ALVzjncmZG+NMeiUyW7bDtnz02XnHa8C8bM=", "IzAltS+OsXJXMreN66kI0yHQw1iujayu5+MZT1C6hVI=", "AQ9lUWNWiBrB2e97SUvJFvHXeXeEUaDgI4KY521Z0Y4=", "OiVhs4eRMlXzRIfQzH8BjS2T+hVrCHe9hb+8RZRFokQ=", "oafugVvdIccBrmnvKuN4TL57gfDA2v7AirnDh+UY7sY=", "a3qgXfg6jfjseH/3hHQamSiR+nGPV2BtLuh6CTEQhqw=", "SDrGqTKg8WxTKrqUQJdaM/Eyz+S5+xXo1KIJD1eEK10=", "cCG4vBYUpaNbtRaC+A7VR63o7FbvIYgKu6RvTA10Qbg=", "nHA/2/dOHAiu4WW2k8a4RxHXMsi1NOd/OBgA1Pu62GI=", "92woLOKhJRxbsKzePiQ/bwCwRyY7XlBqCs2CUJfDaV8=", "umnLmVJB8FP/CjbtYCa1NpWLbzCn2Tac9pT6u6bJKMM=", "JF2nFSxuWMA0QibgSSo5QvJLVhXmdWLNrV2EXXJO7XA=", "/dTViHi4rnmgTK1UJBabNV51K/PrT7toV1Mekw/kM9g=", "A1gyDM7ENQW0vWYD6DuJWFIg7QwFoxP6HZ8ifoVwkeI=", "276NmiZ03iSRw8Wy/7zGpAcG7Z/fnMjpn4VpDqabMXk=", "h7KtH9sS/yDUkPwMBbXr1SCWqp3qEZq4djmPkTstQ0k=", "X3D1Ddw4h1UE9FkxaVAJQUvL6JHV8bk1LA3K68o91zE=", "pdMtrRvYe6FjGoRiDko9bvba9RliU3l1o/V8kMAEllE=", "qKM4y0Xs0IDTjBzNK9y2o9bT7f8PH8zKHDtVIIhGOdw=", "6tn6YkxcXrYjXP5+QKdlO/+OjL/KcuoDcjk8r75twmU=", "PgZMqwD6yvcdMnB1Nmz8QXHuFH3ieQkPKkeXsXy8zKc=", "lQTmxzxhp/aKtb9c+3xhLYHofdvob3SWYPYf5UBwtW0=", "AHiN9BdBGN5AxTFR3tVeAZin0fghjl/WOpBPPQagM4o=", "rAlGFVoqpVawXzeFRWfsNopUkqDU9rH7kdtges1vb0w=", "c+DvPo+o5XKfarxkJlsbdVCToW2akppT3pkrArbqhVA=", "WMGd+spb5KKJFifLCQhED6bDjYr0HCAF5Sqrv9Zv0sI=", "TDOKBDf2SKZuSob0DHUUW5kZT1xs+j2xctb8E2GUSF8=", "28QVCdm9SGrkiNbTmrjzY3eYpeEuFKTySQrT0EyEtvQ=", "JJFO0NCBTFZC8K9ZiSpfsLnD6XUHnRmJ7fuLoYjAh6U=", "9vbDIo9J/HNU7jAWu4lx7h66dUJQUJcjuYOciIQjPbs=", "lvRBfdrvZtwN9XnO5zPjRHwaQFG8zhO/qjBfuJRkJHQ=", "14akeAkqxhnudHYWn0/i/4s1Bmt5N+jsq+P8q9taTSA=", "FbnO0lFK6wykNXEN76OIA0AGAloNY3b7NSJ3jImi6Jw=", "jM4BacYAah8IoO8D/CtEBz8mSEbzVDCplXd09GvneK8=", "GEPcwRudlgrXrUGPajURVSEsqL8EIwXA8/sBEJP3eww=", "w+yjjfNa6QNPAgLHlmbedL3GCWAUj1ej0GAnI0tjbJs=", "jPcc1KeYndrbV2Ee04yQiCEIzwqwK6IjmAL2GvqpfHc=", "9JzCVhLITZGpWgfJKTChfhfcA3QHxNKVayObIw7epLw=", "Z7MLaYWTN3MXWSe031/vJlyZAiknCDNuJfLLzM7Y+SY=", "1DZ7l3QurISNeq/CsNChyis2QgriYVuA21H5a1rZ9IA=", "ePY5ZtCWG9ldSkR2yKUb1P0XwOoTRRr5tE1cDV+J2aQ=", "vyA52P661cZjtpkvYl46d3Qcro9kkwaljvl1GjWikwI=", "whPamR3aRQqcus57wZl/rofi/a+J6RL7sWFUn+rVcsI=", "duqmp4ljtHzu6a/zzMmBycolWZxcgordX8q9eFGYtlE=", "XcDmf3YCckAERv1MM6bEMN+LVR3batNtf04EmdwPO2w=", "Qyqi3fPp8EVFXanHp88uQ2RlkBBr5f08Jt5HJBTTVEU=", "DG66ggu1v9OK0dTq8hud5+Q152vEyRdv0u4XOqYtwsA=", "te6orkOe2Pz5Q5hkGFLRXc/LmJDiZgzcVsvHoD94+wo=", "HpkPgqmo7oBJUJN6SwWkneojVs5mzDrmxlNmDTRMKRE=", "jIn/CvjcKkIjxcq6cefAVVrzvFodEYwGdJLxKCnnDmo=", "DEyVthLBp2raRMxnRr1p9lTDljdjuTE+8ZC0Bl+Fi0c=", "vtckrpTV8b6zSzQ1neIdfxajgv27Fx7+RTfCsFlL89g=", "mcNW7pKqz6ftreHgJEItc8zl9t2VOovY4icLECCT2io=", "7MWw9KOKFLfbSJCujLpEs0GPYnv27k12bTra8WNnK5A=", "N7GpadcqzEPWx7OuqYLoRuDy1CCcPxYvcEd1FoWaSqc=", "RsBrwXtLFRxE4ISuTmK3PttoNYK6GHAJFiSPHFeVq2k=", "JEjDGCQTB/y82CPZAEC6xwCLMTsR5VWL2htIW0xI8YU=", "te2YGxtoste/Znxaww1P13ZPk2Eatqd66rAUPb2Y460=", "U0AvdjpVDV5Jzenc+5GlBkHn6MACBx8Rbwz0xvJJ7DM=", "CYI3p8C2bGCgGVVhNDj/JhvEyLJb2Yz3kdLHdAKkTKo=", "6aNl501zaC+olanKcBqBbXl3SahugG8aZqcXli4thXE=", "SceEavuCUMYO0TzkqRyD+z/D69HIfjQtWdBX5OR5JJc=", "kXc2GQEuY7nN5lUkRRhj9snF62eSorSMdgk6ADhRA8w=", "lIBgj5ykHj0SOa/ABZYOzICkxx1v3MDSmMFujlyCXjY=", "TX/rzyexME3HjKikdci2MO5oiI3UIxpB9DimsVPlzP0=", "V4cAZskN9B3iCyJMIWSHQiooi21Ez6clXopK+RuQpSI=", "Q1/sk8iap0DMvcxCGGG3j/dAlhYDOqfQRkXcz0k9EMQ=", "5f6kpRlPWcL9Hcic6qU+Wt1TNBg+E4dFshIfVOHZFtQ=", "WAsqCr6aAG26rWK8tloYPeaQ6+clHvoS19a7LYhcZ90=", "vQW4yAUC/SiTUqR/qZIZwYj5jNNsL3WUqGEy37aQn/o=", "jzWXCxUFJyA70EHghHAVBW+8kFhRuNUwYMMn4mtItos=", "aYvcC0euun5ZzCDGTUIJmKoGT7mIT/Nv0DxsnzM17us=", "H4aE8RmwZkKaCgKxc9lZcAClMWgzVophMivcN3xC/So=", "VGZn+oI9uxMbwKhcnm9Uz17wK4ZbHw2s4QdEwi0rAFY=", "dEpVWPhYrhMHnbWEH5gQvc7Ae2uEGN9/cclJIh/loxk=", "t91rz6/pgWWxA7DBdohH8NvYzoM/2enzn6zsSC0t2bs=", "MNs146+0ZuKIHnEwqBuA7SvWZDqS8bc337CRlig/cWs=", "5TH+ZKLUHoQqTQQbzyCdHxjUh4WMKilxtSeI4qdtZuw=", "d/JSZopHRJfn2sO+wnnw7RntaDxyRqSM9MZaBjicYc4=", "qxLfPqv37HsnPoj5vSGqqy5C0nvf1s3jltkIIBIM2VM=", "/VarKs1rqkm7KopMANEwzyCB3phvAiE9fNiBYyR3avo=", "NDAFycU2XhOiG6n5TdxFEzxgi/ZLNotUK0gWQtezwfQ=", "AsP0OlRFxTlubn2ilXHOzcYAqrEqR4n8fzN48cZKKsQ=", "QOX3IgcXLK7kLzVdJxxzjfJ8eYuldcRJHecMjvIxiTQ=", "LytrrN399WyZRk/DlDakupcfi43DNX9thNBFPpRtFhQ=", "7zB2xiMwSkkWeIb+3Zm8yUeHxXQywQ7D/Ogm5NhVbAc=", "o92EHmmxcHrKAu/Zld0ocMnaejqJEkdlF1kNPOywIWY=", "FElGkzKNI+Hu9DUcuEAgL1bN0/kvOKGtOrncfRjqpCc=", "X2KtTAOI9QRFJ7YH1YLdhOGvsbRkER3rwp7fMWaa5gc=", "Chqx15cb/7drbb1zXTRheamvk36S3cDU+qcVLln3Gco=", "008TsYtL6oeWOTYeZ2edyd1lwPw5Jo/0LOvNROQtgm0=", "NbUk6VGM+AMwdUlWSMgxy5dCN1J5nqdHDMA2cZIReT0=", "dfGlsTtKxmVHogB8b9atxnHf/+/XFC8Jhcl7d9X9ndo=", "mpEnCid1NjI2DDlM0SpjDzejR8kb/UxcXK9UtXnx1Lc=", "MoOSKS6Q7AR5ltjV1LfEYGXPRvmgwUx/aCkUClkX2as=", "pDba0uxoh3SAgPg5NRyW7vdCD2VueTkEmwJLXptP2kY=", "aWGD6eBZBkYjvH4uBnSBZ1Gzh2D6xFLqqcprXiNDQsg=", "mFAr8fOTaUIEAGY18UZE0SSVWc5mZ8g4zbg6eByhsSE=", "FCGATOtTGuwtGmitQfjOJIzKdZkCsfVmjMnblHvb1pg=", "yE2ExgMHdUZMXBfGejx1MJXiT5yVdum8CTh6zK4jt+k=", "jl8/GKE3lCJFs8dd9/T0901RsRkOkxVNzVqUBzEbqhk=", "TVtnWKGzcqVMcAr8EHjXVuhyFmKn/XrxbE7iqAYhuFI=", "R+X4+m6+mqTsFqZsG+u/lznpCALxgSqbz7KV0Z4UXpU=", "XNyZOxeXdJTJMGx9HPgBmBmo5Qtjx0eNjfBkMnJQ6Gc=", "1OJcDGPwaTgHbunlT5WoFmnft5xWCBoyvtUnkmwlhPE=", "jpRPn63O7jC6qXn+R9guWK/euM5gAY/2aTMR0h5UlFE=", "0F+3qSpLt/vPc5PNWONWp9MYuIE0mBznE4FVIxWTk6M=", "Fko1jZUJan3d2j3EotzZL9eIr11/+9Px7MDCYs0AKTg=", "5U+/J+lQWm3Z8x1mf8LLpbEER0PUKgy4sLBdoJoS13A=", "mxgaPcIX6wbvWM5xnbT7c54QcK1T0LO7RfJJbjczeN0=", "M1mzKMAEAj2Y9k+iQfZQwfCDhX0yVFbsqz3/KtH0Nr0=", "2oCfEDmJOwTyyhnxDeSqOs+7qPj/po5WTgPH3dybk9Q=", "dPx5s9kn389AUJMQWm5moBnUP62QzEoOe/n05CmYfP4=", "ge4YilmR4NfRtddI2Uv7TG9rXf1g9lpr4lfmGlTMXfU=", "9IrioVTsXwU1oTDFu1nJJiFzeAiHzy5rPXsF/l4fC84=", "ziupBqiwA66uKBwjaCh1Eu1MtJv9tCzwoK/yNznrZCM=", "5aoF+8nbkRKtrImoipOPbPYQd+Tn+NXTYQqK2Nby2jI=", "n87sY0FH1MzaPf6uEG+hhR1y/WTrVPjIqVKII0ZoLmk=", "WtAuY2lzNSGhAMdIaEteoJ8XgRfXDroTlBm6g6CUqOg=", "IkdunJFAOpbB5Qi7Oh1bQgHRzCXxtgHlLofLEOJCY4g=", "wYwtZDQYdiL3Qw4LCAg/z+54ShUcx4+S8QDOpNfrnOk=", "CouBgU5N6guNhu3mLrJwCVpgi5sN04ryaHVSorMroZU=", "eUzHELgZXlPw2v7xumYdYxz4DNMCuLU1466zWWUc5EQ=", "rPEh6ils3f8ubRv4zlkAabSVbYuQkIRJuV39V7E03ZY=", "dxg0GjLPsGfA91+DNhjQlqjnSQX3hSu3J+1auNz9dBM=", "taiDG7QtUGabGA1IKfBxeDcEpjyF8sgpvD14FgScFIE=", "zd5kqnPkv2XQGn2NuGmUH9VqSaFY18Xy6xJcf7hw79I=", "Ivdcjos3XngQPRFozhW471tWZ677xd1aX+z+JnJ4/TA=", "hK/p+RPxYAD8tyDWqJYzR6uzniZzQ8Btaj1Eka/RBWM=", "PJZGOo3JTaAlZ4cf3dFS0Uhoqi9+EftA5ntWSDKgHBQ=", "10ow9hTMzBOmGUn/tmPuJO5N62NlMHEaWEDymlbZQco=", "aUeaUgfgKZ3zV/n8NjlXRVIgZlTXdnisHTSGG1oawZ8=", "eqX8qWBz6uPV1b0rTMVos8q0P90kSoajsrWo/aL+ZIk=", "DwAO56Q6Z7dwoyDaZG5Afue2ZJhiUn1oW932CjnCGDY=", "UeZe06AdaUWgc7XZP0eTySt0otjm8im6ZrQ4PG02GGk=", "f8DJkjihauEsGoamPw/DwjlNcn1JDuit7Bg/6NmmzXU=", "/XwpohOz5GoQW5ms7+863uv54vylUslGCMD3Cd2tbwA=", "YLfcGjrKnQy5fmraxrJTU8T1d/scmiYYUyUV1KImHXY=", "VMVuaHVTe5gEzCjRDTNyhDSrt1Sbzzn8KxSPESAbD98=", "SFNaywPbAAikcu6bbfGQECcjLgyCB1G5YqQ9NvpqPZY=", "JWDPt4aHodtlOEeQALarD660d19YP+ikFui/5gbptko=", "QZrE301TM0CwPMAlKMb7+PSmgjgH/2m4gcX1nDGYebs=", "pAMjqROHU79Y4gtNc8CXVMZmSjYkLYWUA4OW4i8IN0k=", "FSESW8aOG+gDJF7MEjBmhtpcrFTSiGeQ9AW+hLTp0/Q=", "QJWAYrZRM2A8hjcJrgK+Apkt6MpdfI1ozekpGCbQsA4=", "FAp2OXWY19UyBKktCeGmlJTr62C8tYDZ+GRdrw3TLA0=", "hLspH7bDCubYK7TUEtTzpkGPxpXaGY0LiIENX5P+CUE=", "hvECDZ4qVvmFn3W1LTreodYmmgdgOsXIYzyRUXFECuE=", "K01IM+CrUw3i9wU3vQGTsP0PKyyEv4wWU4b9+n4jsmI=", "+OGEm3/KRBcoktsVZAJXTx46H7bcLxodhjnlkJyPJ5E=", "eszA+dsJGt85mD3EIOtArSpogzy4KtyZpsLouNzkgjg=", "wAbA2b9L4OuukLupgitOyoLHNkMj2mRftqwI+5JSwvQ=", "TrJWtJYnU6LF/mXCSyMXYJKFKDszQOhbqBsAmMMDne8=", "Y6aLI0OGufU0evWX0FhHhD/Xto+U7n7uSGGhY0YmTnc=", "0MDB3wAPijHp3NiS1QMFJnQMHb9sAQLk+zk2z3K93Bk=", "DG4eRGiG/qEOdOOb9BpuGexRJ8vR/AUzIuV5j3OIUfY=", "DVcUxzvfJBaa2ml362Sn8SbeDku1nkKLdHIijWTCnGA=", "qNxbEwOuPc6pbNZgsvEpGgbMnmbV5+f8D+fSqRvZI7o=", "3/TnlBycZb+po7wbGd7OC1VXZbx6QkJ09AcMtavpWwg=", "VYuP7h0lroUBFZy2ufmTaEdbIdaUJCk00SvmdAYgFHY=", "uvd4mk0iddSCt9QPBlCWv0Hd3PufkJWNm+oLOASghKo=", "7PiH4TNE7ya663Tw89fM4h4hVgscIQpUOKcPjQuDawo=", "MERi0FLzhMzRVJeKV+ZR/7IQoYTirnwFhYSF4wevoAg=", "V5a4TtwFo3exs/EL+9HV2YstL4++jrk4CIsoGezM23c=", "fkj8at/BAs83rNkt1U3Lf+ipue9QQv1mF6hDPUVvAiE=", "Fumwmp3Bcbfy5mxJ3QSJ8NtnPXwRWDktc7exmC7yXsQ=", "ywv7c009jjNIjEpnLIJpHIYylJb4IyQZVYbLlpqsSUI=", "VF1xe9E3E8juFDX7eC0XfmQmAV7T4sohjZEKX+EhB+s=", "VbLRNCJrXOTx377oIJ0xPkXluME8Ehw7+8+GA2k+bGY=", "BUIh3HpONs1K63GpkCCzLcv/Q4HVlbdfZUJZ7JhzDTU=", "Xrfpze7DVEPZ8pvT+A+GbXbzLkIAjhscxBVdnBx3zpc=", "WJ0qpxZoCZbxdFvWuF+iW8KMBnwU7ln5Iz7kA+lhoNk=", "Z35ql9kbD1L/EI/b8oA3+5wwHL5TK8sQogYkuQWzDuc=", "25/p1mRTTfLdzFFWjxLLDyZh95i3kTBap7Xi3h7Au6k=", "N6wH5VjdzxXLX2COoMa0xbe+qj8cw6vknqCTIwZD85s=", "0D6IM4FBqfThwj7978WLBDdSHrV8xysGeM72e/+eK7E=", "2Xs/wjOy3PPIpnjwi1p9SOTGSYPZ4Rh0jAVsOJojB5Y=", "f9Izl1eZ88okZp0L25EobvvSZhfAWOCv0PAQOgi+kgU=", "91dAZeOJxC5Wpwj5USbxPPYyuVciCdfUAeW/dk/fr/I=", "4iGyAr6upjLX3vJUA0HaQ7M9eCiBnUiV6jN7hU/1qOY=", "tcHQwua8tErU9Lok35HItISxZyMjdCb5yUzhupDkTb8=", "IFb4b1qNKLIlADNoVn1OdKUXtpQJZxxHdpz+jwdCsSw=", "pV3lLCwEkOPYB8WgJKG9sIVa5XAu//3avEfSaC3QfPQ=", "O0f3TDPzH4hqu5D3lDEgxIIO4n12QIwMMAXF8342/4U=", "wkqzE1AMhpH5e34PcgedswCYI34J5He5YkLnafcAXkY=", "X04bVsM9ngVEkZu500xCP66PYRBW0fpBQLlgvYMs/bQ=", "03+N8HYKmAwkAptbImCROo9Uk+aNeMMCSB7e1gjweJM=", "M7bW3NsxsYoM4Qm3d02j6k7f2WWmcYi0cmJOLn+eTCg=", "LH7xSfrgBK5sGdpIn23qbaeFRJ+4gb+eBl1ctx8Xt9A=", "pv/7iOidmYqFnJiUQyCa7ObsGfHAfXEwaoiR0CZAyAw=", "7juJ8La2Ki0tI933HLyPZ4J+cwj8J62vUMIjSXsvmog=", "iZiwr/ZBXzecywnS5yyIr2cgeifZWxpJUhuQ62fenVU=", "YoIyI0GDS6wnPiDmbJxWgplZzVp5KQo5HVKSJ5Fm7zw=", "7sXU6mc3ScYrusfYEP3aJokrN8/5PqogV17Ag7Tjy+Y=", "UTdhH9eP9ELKq+ttcZtLPGz7aC1a0drufCbz95re/dk="], "CachedAssets": {"RLPqTxFobI7saIdeFf1qWpFnz7qUf2ZW++CauefmwUU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\g9w5tdrjvt-whph9uh8dy.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8jofbi9ww", "Integrity": "ka3JlDStzfWtIK6W3SJzGcPFjBUdQ5akm3yIZPJIxtw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap.css", "FileLength": 474322, "LastWriteTime": "2025-07-10T11:22:49.7457955+00:00"}, "FH1AtHtTIDHa2vkx4XdlBiNzTuem4qhNfYuYTYUTWtw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ev84ybuhfb-i3kjql666c.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qd3gbhwiqw", "Integrity": "4jupm/4+2SWkpHKOaRxXTktjhwzIrI3ITHKRJAi3DD8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap-dark.css", "FileLength": 473847, "LastWriteTime": "2025-07-10T11:22:49.7638578+00:00"}, "SurCd6fKPR4iHqTD06ICfi56XFBx0jKOTw+ICFkc3sI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ll0ck2c3pw-1x110r870b.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "70pj9jfzzj", "Integrity": "qXW9D2xqb0+9nXEwrPB7Tgy2ZU3L2Yf4YFEAUY0yJFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap-dark-lite.css", "FileLength": 473861, "LastWriteTime": "2025-07-10T11:22:49.8893446+00:00"}, "iUfhl8Y8Al0DX8Pej3v76vvRE5x+9cg2aA+OEFYoMBA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\m9pq0qobmp-o4xbqlpium.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rtnhhgtg9a", "Integrity": "31PUzmB2xr487tdkV4g8OcBjTUaXrYuIsL2puwmAtDY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap-lite.css", "FileLength": 474324, "LastWriteTime": "2025-07-10T11:22:49.9928961+00:00"}, "25R8uL6d+7aoKlAF82EAWqj72f/byChFMqa0ad5fPXQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\g0kkhqlx3b-utj2dchh0h.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap4.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap4.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wc73l990ls", "Integrity": "AC83p3Txj5G0YXdeYkeygfGaRW3EUg1ouovd30G+X28=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap4.css", "FileLength": 464620, "LastWriteTime": "2025-07-10T11:22:49.8954167+00:00"}, "ES2P44WCF6Zc38qva7DBQ7wHwdKhbOM72yitNlfOD4w=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\fx6f6w99v8-yhdvff49gt.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap4-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap4-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m61uw7hr8c", "Integrity": "gjKTcro4kfDqIPz50Q+nqDtJHMe6/fpO8VLjKfCcHMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap4-lite.css", "FileLength": 464630, "LastWriteTime": "2025-07-10T11:22:50.0037095+00:00"}, "BQ/Bh9x2bxNqV2UUHKHPhGyN346cAoU8i1d+sZPvCbc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\czad4b1t5w-94crdf3e51.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uh5ba0yvnb", "Integrity": "buW6JdcGODqsHoptvJmAzrOImagrz9FgXjSGwISTssA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.css", "FileLength": 472071, "LastWriteTime": "2025-07-10T11:22:50.0907175+00:00"}, "q0PhSQCVgJks7xw+p/sWR3p4G8uH2TunYkztZ4jvp9g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\w7wzav3zkk-3rgcbimoyt.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ix8d55haq1", "Integrity": "dlcLtuKVJ40xOJxk3lmIe9oxl/t30AFqCPhkSh2bUY0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5-dark.css", "FileLength": 476207, "LastWriteTime": "2025-07-10T11:22:50.1743029+00:00"}, "h0gxOf8vM6EICUVednlCWEpswCh68LAKFVDFVw1vMMo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ezik3was9u-xlrgv2ppux.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qni8cs3zq3", "Integrity": "dnzpz/0ALKwPDBBWQiCVjVtMw3vQqYjzI+nG+XWOsyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5-dark-lite.css", "FileLength": 476197, "LastWriteTime": "2025-07-10T11:22:50.2663488+00:00"}, "6/AvJOdiMQeB7ya0BlnqwFuytQA3UUrOVQNklkOsPe8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\0gmeqlood7-4c83uxrswk.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "by2ulxp2yz", "Integrity": "jSz+MEOCYR6I1JDvXy98oA4pxamcRZqO1l7YCd7WWFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5-lite.css", "FileLength": 472072, "LastWriteTime": "2025-07-10T11:22:50.4411166+00:00"}, "fhPOMyAThTK25Bos74kTxD2IFpBR7OMVVp482yhDzVo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\rceg06wfiw-9b8xedne3p.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n8y7n8n28u", "Integrity": "6c+QCa2C/UiJVwL0gIE9kJeCR3v74SY1vQUXF+Vcr34=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.3.css", "FileLength": 477154, "LastWriteTime": "2025-07-10T11:22:50.6194474+00:00"}, "6UxDLp1pcbQXtmxt/Xecql0pKm7SnSJhaJvISkQMi+Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\5q2prff4m2-i6bp60k3fk.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8mcl2wxbv4", "Integrity": "4Dp8R/7VEYCMAdRnYJGT3eKWJehh05mFIYIcEtijBJs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.3-dark.css", "FileLength": 476295, "LastWriteTime": "2025-07-10T11:22:50.7209363+00:00"}, "NxdCmeB8ZrffIPs2u6o7AFaQ4nYTotpN21hQ5P6EgL4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\awwokdaaq0-cqejuz8qps.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o3pwpoeoo6", "Integrity": "7L/v97DFvwLWp0YTcWBWJF/6BYztdCXyjNmtEkVrX1U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.3-dark-lite.css", "FileLength": 476300, "LastWriteTime": "2025-07-10T11:22:50.8166094+00:00"}, "x4UiC9Shwu8gwrFqd8HQr/KNyWRHpzm0SLiFvFFMrt0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\984b865z1b-fpxrc2qkh2.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9pale0b34d", "Integrity": "k5hxQ2CAAb+N7Qd5H/bVJVnfqJo6HlziOwAYzRy/lUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\bootstrap5.3-lite.css", "FileLength": 477157, "LastWriteTime": "2025-07-10T11:22:50.8986868+00:00"}, "rczMxIFtaHeZnCk5qOmY0nE35qov1QG2LUbJ7mXo9oU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ytu8lus5q2-titr0y3e6c.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\customized\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ni3x0c4sn2", "Integrity": "D4AhdhgCeAVY0fofDZAeYk2jE0ZdsF0bJG50lYu+noA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\customized\\material.css", "FileLength": 502126, "LastWriteTime": "2025-07-10T11:22:50.9768621+00:00"}, "HUXdU5BpBJthak9nu3jz/WRPupPd28WLXwFu6fWTkSk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\mpbuet7hpe-fg3l2q9ila.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\customized\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "71n9k0ij4o", "Integrity": "Ja+WC1K50xPJEsVTjUWWILW/OJNhX2qqFsA60aKgfjY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\customized\\material-dark.css", "FileLength": 510071, "LastWriteTime": "2025-07-10T11:22:51.0486011+00:00"}, "MtMY2X0vO+Azy54KVym/h3hZw8O7jzl9Lw6FvTtoxpI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\pxx57cfmdu-qr94prjpan.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\customized\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5uu5wqekx1", "Integrity": "XdNxu/KbTm8YM5/mariyYazRvSU8h8A5jEJoqg7Rbgs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\customized\\tailwind.css", "FileLength": 436028, "LastWriteTime": "2025-07-10T11:22:51.1025215+00:00"}, "cpBYQvbYkJD262Y+79gEssqkgCj+oqLOmXzZ/M2dMVk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\yu10ux9zg2-vqaa9nckmz.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\customized\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "utodxg6v7z", "Integrity": "DiAnJqOsWByQsHOXQ1QpAGCtOU635rzTV6L0YEVaJG0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\customized\\tailwind-dark.css", "FileLength": 438894, "LastWriteTime": "2025-07-10T11:22:51.1634281+00:00"}, "ZqhKd1jjyqGTWPBUOYa8vIxcl9SwdRn4ezAIfO2FXX4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\xj9e5awasb-9mnlgchzvl.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fabric.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0jph15z16l", "Integrity": "fOBCjIpKdiNZEkUjRlU1A6TRgwePxnxJii1RnqpwP+Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fabric.css", "FileLength": 453046, "LastWriteTime": "2025-07-10T11:22:51.2274028+00:00"}, "FSy3lgvFi5MxwlGZ2lRCd9cBbuHA7g3cDe/IRlLC2JU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\f1vaqyeoju-vt4bu1zauf.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fabric-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5jzg7f5b2p", "Integrity": "H9VCL5zYvkeVXEORBmQ8XYK18I5BHLFXMLVAh7SKEbw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fabric-dark.css", "FileLength": 462339, "LastWriteTime": "2025-07-10T11:22:51.2839586+00:00"}, "2n/jnzPRTGipnJxf/gAIgnVBsSqLiUcfFgKwEYWI8BU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\8fhnih39yd-qs201mq4gk.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fabric-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "avn3n2kc2r", "Integrity": "lpqrJnDIKY85SfNzRia0so5OdlWIrIq5Bgyux1OUid8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fabric-dark-lite.css", "FileLength": 462336, "LastWriteTime": "2025-07-10T11:22:49.8543463+00:00"}, "6BeXDQCXAv+5/8G+IA0SiMo5jWOLRRPlS6dZIOBGkpI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\aqas0gzsh0-i7agl5qmgk.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fabric-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t34kpkv7q8", "Integrity": "yYrk0PVbj1AbOekyQ57DHc+HuagPfPNBa3eeOSUT8Mg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fabric-lite.css", "FileLength": 453056, "LastWriteTime": "2025-07-10T11:22:49.953541+00:00"}, "qzYw23WpATn29yCai0IFfwJ/tGDqt3lvprTz0nyCiw0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\r5ssla993m-o7ot2a0c1i.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uyebfcj28v", "Integrity": "hu+WiJHaGZAPAkcd6VdHwAeRnQui/TAV5qLyDLReXOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent.css", "FileLength": 451807, "LastWriteTime": "2025-07-10T11:22:50.0642265+00:00"}, "fOzIU/WX53sBvtnl72C+21Pzhz/WhmnB2E/CW5Tyyow=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\yqbsc2e4c2-hb2rzx77hp.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "32cq4o2p5n", "Integrity": "dfzz5Q3Uqwarqh0We9GXmh18oB3K4WG4tbaX/9VSpfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent-dark.css", "FileLength": 452216, "LastWriteTime": "2025-07-10T11:22:50.1849961+00:00"}, "ENyJnoaloYZRBrEElBJTJFeU6SR1QEaft8LyADXLbhY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\2dszv1c0dc-00jrbgoo05.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4r83r04c46", "Integrity": "8HXms9XzXdfnwzFEYNXaIZZFfCAhj8t0KZt+VZwDg9Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent-dark-lite.css", "FileLength": 452221, "LastWriteTime": "2025-07-10T11:22:50.2964395+00:00"}, "Q3Py2w+10Pv7wE3FMtENQ2e4lovzzYK9CrLIs4Z5PlE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ubxng02trl-65l9kxo85p.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tggpm4j6ii", "Integrity": "nTUOGACr4hZt9nxGR+q+JVjCql8uaQNPA1vJiiqcvXQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent-lite.css", "FileLength": 451811, "LastWriteTime": "2025-07-10T11:22:50.5064099+00:00"}, "MriJojb+CZR8k5KMkaRy2udd66BpA4kdZl2ZwwEHaVA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\h56puxhs03-1ykyrlysvi.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hrrxqa1szz", "Integrity": "zdxJnjRbIq9l9c33I0sykFbTxFDMoj2TJYdJIPY0CBk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2.css", "FileLength": 486257, "LastWriteTime": "2025-07-10T11:22:50.0857172+00:00"}, "5RnQVRqq5c0La9xXUoqdQfUZnrizICmLmv4AQPNjXU0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\oaindoncb5-82rmm1dtqn.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1j05b1cjsx", "Integrity": "7aCrQSiEeO1UYUjN5v4Qz9eea34ThDdzgwqi1mIgej4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-dark.css", "FileLength": 483320, "LastWriteTime": "2025-07-10T11:22:50.1861133+00:00"}, "s+Iaalg4nOIWUk8+UJpXmfDS42Nf4TQ2Sgyr4RLxizI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\xhfo9ezgkd-5yzz4za2su.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nuw37omp1t", "Integrity": "8c+Vb4AL4lT5ZCzS2LWQW1d7sUcBBcAHQih6EHj0JhU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-dark-lite.css", "FileLength": 483329, "LastWriteTime": "2025-07-10T11:22:49.7841548+00:00"}, "x5k9gFR/ALVzjncmZG+NMeiUyW7bDtnz02XnHa8C8bM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\1e4urs263g-351ns652dd.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hm1z2mln2y", "Integrity": "B6Or+XL2OfPYzm7Tl4DtpgQ8fTYds48CYO6p4HUPfA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-highcontrast.css", "FileLength": 484817, "LastWriteTime": "2025-07-10T11:22:49.8913483+00:00"}, "IzAltS+OsXJXMreN66kI0yHQw1iujayu5+MZT1C6hVI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\4qsv9tgae5-ok92gnhgn2.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2x5zpxhgi4", "Integrity": "SgqBKK8OLrLs0RdiOYfjo2hNF/jujmG0ZNKQBE+zIxI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-highcontrast-lite.css", "FileLength": 484817, "LastWriteTime": "2025-07-10T11:22:49.9967157+00:00"}, "AQ9lUWNWiBrB2e97SUvJFvHXeXeEUaDgI4KY521Z0Y4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\lf21o06s6y-ofe2zo928b.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uugha45443", "Integrity": "lpl7rjKHiFRIIaTOGQtTXBnfDMctporwKuUgfFDia3I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\fluent2-lite.css", "FileLength": 486262, "LastWriteTime": "2025-07-10T11:22:50.1283455+00:00"}, "OiVhs4eRMlXzRIfQzH8BjS2T+hVrCHe9hb+8RZRFokQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\8u73xo5u52-cltxiimpan.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yr7s32exc4", "Integrity": "XmH70XnHCRda33sNf05W4huzIAvowXhRD7xhG0aChHA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\highcontrast.css", "FileLength": 451398, "LastWriteTime": "2025-07-10T11:22:50.2305776+00:00"}, "oafugVvdIccBrmnvKuN4TL57gfDA2v7AirnDh+UY7sY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\sa1475ed2m-jyx5tqf1wt.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "326n5bh7gx", "Integrity": "lYDSrkQO9z6AYvVCQlmVh3MqcJTg6dcHM5YhecXTlEQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\highcontrast-lite.css", "FileLength": 451415, "LastWriteTime": "2025-07-10T11:22:50.3677322+00:00"}, "a3qgXfg6jfjseH/3hHQamSiR+nGPV2BtLuh6CTEQhqw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\w0orhpiaxo-qb8mqdc59t.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o954d64cra", "Integrity": "VMcldbJqd/3VV+fJDlpC/UqjYL01kpwYXtd/UeMMWHc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material.css", "FileLength": 502165, "LastWriteTime": "2025-07-10T11:22:50.556035+00:00"}, "SDrGqTKg8WxTKrqUQJdaM/Eyz+S5+xXo1KIJD1eEK10=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\4ulei2vwud-1cbp8x3s91.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hdedp8qhax", "Integrity": "BUPX4b64KIsdh82KRe8MiVhvj9B+i60GwqwaSyBeq6k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material-dark.css", "FileLength": 510073, "LastWriteTime": "2025-07-10T11:22:50.3464808+00:00"}, "cCG4vBYUpaNbtRaC+A7VR63o7FbvIYgKu6RvTA10Qbg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ay4l9je90m-bkwqcdta32.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ebv1j0x7v", "Integrity": "aFgTGmlFuKcYum86sYMfX5IoI0ZLCrPjQr9CPNVDOas=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material-dark-lite.css", "FileLength": 510074, "LastWriteTime": "2025-07-10T11:22:50.5673926+00:00"}, "nHA/2/dOHAiu4WW2k8a4RxHXMsi1NOd/OBgA1Pu62GI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\fsp2o9negp-i4yb34ulf7.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqj3sxw5f0", "Integrity": "VHlY2Fab1aexXczdBkv7TKwHKgvBRP9yQDghKagMd7Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material-lite.css", "FileLength": 502171, "LastWriteTime": "2025-07-10T11:22:50.6893348+00:00"}, "92woLOKhJRxbsKzePiQ/bwCwRyY7XlBqCs2CUJfDaV8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\s2yf70i8pu-w1t5l0bda4.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6tcxpndh46", "Integrity": "5rsQAuCOhQT11re2U+6BvfBSOZbmUnQfG36BRvrS4vU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material3.css", "FileLength": 452721, "LastWriteTime": "2025-07-10T11:22:50.7923405+00:00"}, "umnLmVJB8FP/CjbtYCa1NpWLbzCn2Tac9pT6u6bJKMM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\wa7m2rhtxr-1fw3u0jo2i.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gyeay29ld7", "Integrity": "OOR/F7cfOatwf5E4zlvMG0ABu4q0qG0Hci2AH06Ck8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material3-dark.css", "FileLength": 452348, "LastWriteTime": "2025-07-10T11:22:50.8790301+00:00"}, "JF2nFSxuWMA0QibgSSo5QvJLVhXmdWLNrV2EXXJO7XA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\yjifzcnj40-gee2jdsvyr.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1thoeg5mbj", "Integrity": "27Fx34ynykXcJMwbIj567oGROwDPAHNFZ50jQzhmUWQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material3-dark-lite.css", "FileLength": 452362, "LastWriteTime": "2025-07-10T11:22:50.9480811+00:00"}, "/dTViHi4rnmgTK1UJBabNV51K/PrT7toV1Mekw/kM9g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\wkxfmls346-bl13kyt8lt.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qu5439qykf", "Integrity": "or27DV/vGAtC2cPjGaao1jJyffzjiyGEuSvGzxWYnLM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\material3-lite.css", "FileLength": 452724, "LastWriteTime": "2025-07-10T11:22:51.0137259+00:00"}, "A1gyDM7ENQW0vWYD6DuJWFIg7QwFoxP6HZ8ifoVwkeI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\h5th63hdh4-7pplzasn08.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgfmugf0iv", "Integrity": "P/okEyWjImnAm82+NiKiCoKxP41R6nDmJgiKO5LyDBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind.css", "FileLength": 436122, "LastWriteTime": "2025-07-10T11:22:51.0755025+00:00"}, "276NmiZ03iSRw8Wy/7zGpAcG7Z/fnMjpn4VpDqabMXk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\sxqxspocya-q4acqztfpx.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b45jiywnd", "Integrity": "77+CTzP6sRDbDpnCYCxQ1AUTuyGa4KUaMA4QZGI6Wpc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind-dark.css", "FileLength": 438980, "LastWriteTime": "2025-07-10T11:22:50.6313451+00:00"}, "h7KtH9sS/yDUkPwMBbXr1SCWqp3qEZq4djmPkTstQ0k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\rd6io8kvbb-7meyybutq6.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "35znexb8bt", "Integrity": "WUzSELEYeAehefPLUQwZNJXt/oL/AQTNqI405AI2QjY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind-dark-lite.css", "FileLength": 438976, "LastWriteTime": "2025-07-10T11:22:50.7329149+00:00"}, "X3D1Ddw4h1UE9FkxaVAJQUvL6JHV8bk1LA3K68o91zE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\g3fd5cikf4-yj8hpavwps.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qybpjcfmhl", "Integrity": "LuT2zKXKRcbMZqGoIZEjOGPI3KlDkkf67VTrqWg1aow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind-lite.css", "FileLength": 436126, "LastWriteTime": "2025-07-10T11:22:50.8166094+00:00"}, "pdMtrRvYe6FjGoRiDko9bvba9RliU3l1o/V8kMAEllE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\k08v6vvvwj-ipgmxz4e11.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xjnv57c2rm", "Integrity": "A5YKEMSZKkL0/PfqMPyj/ZrBUpz87pzXNN7mUAkJl5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind3.css", "FileLength": 495522, "LastWriteTime": "2025-07-10T11:22:50.9020792+00:00"}, "qKM4y0Xs0IDTjBzNK9y2o9bT7f8PH8zKHDtVIIhGOdw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\npcbabgmoa-h23sj4fdft.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dfx40h8886", "Integrity": "OeaiTE95obZPNxIhJYNoAXwBXOxytnMmQ+cZFZHFQgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind3-dark.css", "FileLength": 493684, "LastWriteTime": "2025-07-10T11:22:50.9810254+00:00"}, "6tn6YkxcXrYjXP5+QKdlO/+OjL/KcuoDcjk8r75twmU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\4cc4cl8qb1-rnlp968la7.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hfrbv8u11r", "Integrity": "nR7XY09FOJt1MmwkJ80eVY5jKUcg8dvFfoqX2DOBprE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind3-dark-lite.css", "FileLength": 493688, "LastWriteTime": "2025-07-10T11:22:51.0511127+00:00"}, "PgZMqwD6yvcdMnB1Nmz8QXHuFH3ieQkPKkeXsXy8zKc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\3itjbw64bu-bkc2k4qqy6.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4n4vi1n4z2", "Integrity": "wHBjXRv4sGqNdWZRZrluJBh0OCm34DDhLvrvfRP+vUg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.39\\staticwebassets\\tailwind3-lite.css", "FileLength": 495527, "LastWriteTime": "2025-07-10T11:22:51.1287633+00:00"}, "lQTmxzxhp/aKtb9c+3xhLYHofdvob3SWYPYf5UBwtW0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\2tby22c7r1-lbmfub4x2c.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/data.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\data.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w2ns2gge4d", "Integrity": "2GVSGnT59vcGlVitHB+5zddCAYYlrrjmSkn9iGOzQxg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\data.min.js", "FileLength": 25018, "LastWriteTime": "2025-07-10T11:22:51.1351404+00:00"}, "AHiN9BdBGN5AxTFR3tVeAZin0fghjl/WOpBPPQagM4o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ddg9dsjctj-tz7qeeb3lo.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/navigationsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\navigationsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hdnhyqbtyz", "Integrity": "lYrZL2N8lyw+JJHNoh/FonCxacX08zH73wpm2RWMmg8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\navigationsbase.min.js", "FileLength": 3958, "LastWriteTime": "2025-07-10T11:22:50.5673926+00:00"}, "rAlGFVoqpVawXzeFRWfsNopUkqDU9rH7kdtges1vb0w=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\s0a6p2lsn4-e21v6zo5rh.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/popup.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\popup.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7onf4450p8", "Integrity": "aXYPsS1Pv2uxeujomV2ss5wRsntTFKSUNxtJv55W/3U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\popup.min.js", "FileLength": 4239, "LastWriteTime": "2025-07-10T11:22:50.5695303+00:00"}, "c+DvPo+o5XKfarxkJlsbdVCToW2akppT3pkrArbqhVA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\bmz5nvmby1-io6jyjpz2x.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/popupsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\popupsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "15fxbjs54c", "Integrity": "5eEsh070qSBjrq1yWBj3HZo2NvVXjSzsOyKezlaIXvw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\popupsbase.min.js", "FileLength": 4445, "LastWriteTime": "2025-07-10T11:22:50.5732684+00:00"}, "WMGd+spb5KKJFifLCQhED6bDjYr0HCAF5Sqrv9Zv0sI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ut6zzcs2sj-x30b4btdi8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-accordion.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-accordion.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qt83o1srzz", "Integrity": "knQmgoM1pdrr635LuSU9eRK8Cm4WcDcWjMTPaVUPX4M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-accordion.min.js", "FileLength": 3434, "LastWriteTime": "2025-07-10T11:22:50.5829829+00:00"}, "TDOKBDf2SKZuSob0DHUUW5kZT1xs+j2xctb8E2GUSF8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\xhreyxaaqf-uyzuj6izww.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-accumulation-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-accumulation-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bss7pgy1ad", "Integrity": "e+a8UEUtmIJ6IJenyufflwjbuZHBWosWk9QgdH6CLZk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-accumulation-chart.min.js", "FileLength": 6228, "LastWriteTime": "2025-07-10T11:22:50.5851678+00:00"}, "28QVCdm9SGrkiNbTmrjzY3eYpeEuFKTySQrT0EyEtvQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\d405vrip82-0d3im5x6be.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-ai-assistview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-ai-assistview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c3mfix483e", "Integrity": "aCyffcAFLledMtjkKhwrGwvqorkBlmgm78TEXUFQPbA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-ai-assistview.min.js", "FileLength": 3472, "LastWriteTime": "2025-07-10T11:22:49.6377609+00:00"}, "JJFO0NCBTFZC8K9ZiSpfsLnD6XUHnRmJ7fuLoYjAh6U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\in9q5p7h0u-357olvbumh.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-barcode.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-barcode.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "83oa57wtn5", "Integrity": "jIbzsFuWYo3Lh4I78u+Afy8xjYtnvb28ugOfHcMQdf0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-barcode.min.js", "FileLength": 2547, "LastWriteTime": "2025-07-10T11:22:49.642206+00:00"}, "9vbDIo9J/HNU7jAWu4lx7h66dUJQUJcjuYOciIQjPbs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\0f3xp4yca7-l2iq22gn9s.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-breadcrumb.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e4ako1t5l9", "Integrity": "PjylcbhLf3N2TWJOoKEkp3ENm/Pmpajdyo6bOyDdGWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "FileLength": 1790, "LastWriteTime": "2025-07-10T11:22:49.6442052+00:00"}, "lvRBfdrvZtwN9XnO5zPjRHwaQFG8zhO/qjBfuJRkJHQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\9oodxfd7jy-03gc7tui9m.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-bullet-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-bullet-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pqf7nfpdgp", "Integrity": "j9EHTl3UgyeBiaC9CbkGNSW6v4XWx21YmsF0It/1jDM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-bullet-chart.min.js", "FileLength": 2449, "LastWriteTime": "2025-07-10T11:22:49.6474168+00:00"}, "14akeAkqxhnudHYWn0/i/4s1Bmt5N+jsq+P8q9taTSA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\iyvfbpj4yj-igefjj33v0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-calendar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-calendar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdn9xdzpmf", "Integrity": "aZx7ISpRdfOQzauZpUoH4XTGksuLKJ7oBSf0jJXxLkU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-calendar.min.js", "FileLength": 1020, "LastWriteTime": "2025-07-10T11:22:49.6484169+00:00"}, "FbnO0lFK6wykNXEN76OIA0AGAloNY3b7NSJ3jImi6Jw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\44mfw27k7i-q6zs3dy0m6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-carousel.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-carousel.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2mqmr1pv25", "Integrity": "xuAoC6WVWK7eFgjGRStKOiBlUOjyqJGEWmWMqCGXETI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-carousel.min.js", "FileLength": 2136, "LastWriteTime": "2025-07-10T11:22:49.6536053+00:00"}, "jM4BacYAah8IoO8D/CtEBz8mSEbzVDCplXd09GvneK8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\cfvyxuh1qt-mxamax5ldy.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3foomjd9h2", "Integrity": "YVwDb77NG9KzOAoJWWQbCBHMm9cjGWc7TqFaNK7lSwQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-chart.min.js", "FileLength": 54371, "LastWriteTime": "2025-07-10T11:22:49.6679613+00:00"}, "GEPcwRudlgrXrUGPajURVSEsqL8EIwXA8/sBEJP3eww=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\wye317dvcg-hxap49hkgy.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chart3D.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-chart3D.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vvv7x5s6rf", "Integrity": "K9I6jl+EGLNCz5AT9UasvONsVbGQl31afPqSPxI/+So=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-chart3D.min.js", "FileLength": 15894, "LastWriteTime": "2025-07-10T11:22:49.6709606+00:00"}, "w+yjjfNa6QNPAgLHlmbedL3GCWAUj1ej0GAnI0tjbJs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\1ekojsgjxz-94qtbqhxcg.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chat-ui.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-chat-ui.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nnhjxf6djd", "Integrity": "I90lzV6P3tQknA0iObV4jbyOq+V30K4bi1g+HQDgKvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-chat-ui.min.js", "FileLength": 3736, "LastWriteTime": "2025-07-10T11:22:49.6779456+00:00"}, "jPcc1KeYndrbV2Ee04yQiCEIzwqwK6IjmAL2GvqpfHc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\y6tbkm7tzg-keqr3rvc3y.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-circulargauge.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-circulargauge.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ygivoqpam1", "Integrity": "Qj9zf7Lf1IZ9vYY/pOWFeuXjOpvvJcx4seMRyavsp8I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-circulargauge.min.js", "FileLength": 6799, "LastWriteTime": "2025-07-10T11:22:49.6830848+00:00"}, "9JzCVhLITZGpWgfJKTChfhfcA3QHxNKVayObIw7epLw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\elmu9o8lon-8d532z0sg8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-colorpicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-colorpicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3qefxv0ce5", "Integrity": "S5WPo+GhaKUVmBMOtVLf5TrNyJDrvbj6Em/LOizUm9k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-colorpicker.min.js", "FileLength": 2152, "LastWriteTime": "2025-07-10T11:22:49.689084+00:00"}, "Z7MLaYWTN3MXWSe031/vJlyZAiknCDNuJfLLzM7Y+SY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\rwu2eraxoe-yjtvgl7qyf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-contextmenu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-contextmenu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "as3c66jq40", "Integrity": "Kuz2AkBGiCYo/D7LFTxJh4ZrYvjGJrmLgfILTOMlQ+g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-contextmenu.min.js", "FileLength": 4908, "LastWriteTime": "2025-07-10T11:22:49.6901923+00:00"}, "1DZ7l3QurISNeq/CsNChyis2QgriYVuA21H5a1rZ9IA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\12tizwxlsz-ezzcrrn85o.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dashboard-layout.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-dashboard-layout.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i8vc0zgzky", "Integrity": "3qriSjBox2mD4hBBLKlaiOfug7mSaIQ2PxFmfRQYaOg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-dashboard-layout.min.js", "FileLength": 10434, "LastWriteTime": "2025-07-10T11:22:49.6948563+00:00"}, "ePY5ZtCWG9ldSkR2yKUb1P0XwOoTRRr5tE1cDV+J2aQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\koe3iulnl6-aa9yqedo1y.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-datepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-datepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3vkdexyk67", "Integrity": "37w+0CzyOwcRV6A9rzAFH9LrHdSM+HgFljjRqydleEQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-datepicker.min.js", "FileLength": 8896, "LastWriteTime": "2025-07-10T11:22:49.6994544+00:00"}, "vyA52P661cZjtpkvYl46d3Qcro9kkwaljvl1GjWikwI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\cpm9m8wx2t-0miwb5agq0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-daterangepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f6oqyrbge2", "Integrity": "51ppvdKPaeQBo2kcOu8ntT49py9BuE5FOWO44GqTpfQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "FileLength": 4036, "LastWriteTime": "2025-07-10T11:22:49.7101518+00:00"}, "whPamR3aRQqcus57wZl/rofi/a+J6RL7sWFUn+rVcsI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\d4g29a2e03-oerohn7qem.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-diagramcomponent.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-diagramcomponent.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1vsghjuwnn", "Integrity": "HbNoQZFSNppl0xH02nfjtrSNiXSOuEpzdbi7HJMqQXQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-diagramcomponent.min.js", "FileLength": 16586, "LastWriteTime": "2025-07-10T11:22:49.7146984+00:00"}, "duqmp4ljtHzu6a/zzMmBycolWZxcgordX8q9eFGYtlE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\xc1o58ypca-wdij1owp2w.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dialog.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-dialog.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aknu7yaxke", "Integrity": "Nh70oCkpL4fiNzlh+ODNuvMqrznpI8OhpxHOCuyCfmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-dialog.min.js", "FileLength": 5970, "LastWriteTime": "2025-07-10T11:22:49.7167554+00:00"}, "XcDmf3YCckAERv1MM6bEMN+LVR3batNtf04EmdwPO2w=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\e2fdz9um82-18nhfya3jl.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-drop-down-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vys4rv928m", "Integrity": "p1IlivlK9ahrUCgyZ7Uuyx1TS26XaVoYClwA3+/5dfc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "FileLength": 2726, "LastWriteTime": "2025-07-10T11:22:49.7203925+00:00"}, "Qyqi3fPp8EVFXanHp88uQ2RlkBBr5f08Jt5HJBTTVEU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\3s9nhql60o-uktwrpyegh.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dropdownlist.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jjqpkcyhdk", "Integrity": "YQnepZOffhapb5sR/B9rUsTDmNVwJ4lK13Rh2n9oPxI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "FileLength": 9889, "LastWriteTime": "2025-07-10T11:22:49.7254547+00:00"}, "DG66ggu1v9OK0dTq8hud5+Q152vEyRdv0u4XOqYtwsA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ebbn4z5zip-52uyzfnl79.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dropdowntree.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ofxeinwykd", "Integrity": "PNE009xWKtX84/P8IEgw5Yi6Apj1IqEovUmrL7sxC7w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "FileLength": 5925, "LastWriteTime": "2025-07-10T11:22:49.7342383+00:00"}, "te6orkOe2Pz5Q5hkGFLRXc/LmJDiZgzcVsvHoD94+wo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\w2cakg9ukp-5mrrgn6zmw.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-filemanager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-filemanager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b3vvsevxd4", "Integrity": "J3MfaPsG/hYpIMDBj9ylqzkWA2QOnv8VIiPDjJEuwF4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-filemanager.min.js", "FileLength": 7065, "LastWriteTime": "2025-07-10T11:22:49.7382436+00:00"}, "HpkPgqmo7oBJUJN6SwWkneojVs5mzDrmxlNmDTRMKRE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\uehbyupll5-ylylhfqk0p.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-floating-action-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "75wyk9o6r1", "Integrity": "Oy9n9lb+0+hTMVNOaxZPM2m0kx6ScyK9xLab98BFAIw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "FileLength": 965, "LastWriteTime": "2025-07-10T11:22:49.7392434+00:00"}, "jIn/CvjcKkIjxcq6cefAVVrzvFodEYwGdJLxKCnnDmo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6ytg84iue8-dy3jgizorb.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-gantt.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-gantt.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g81j27iklb", "Integrity": "AGt46yHWScMgXbGsNNjg40oon+LwozaRrC3G5PWWchg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-gantt.min.js", "FileLength": 15002, "LastWriteTime": "2025-07-10T11:22:49.7457955+00:00"}, "DEyVthLBp2raRMxnRr1p9lTDljdjuTE+8ZC0Bl+Fi0c=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\fidxlt0g3v-kt5g2zj8z3.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-grid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-grid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fzx3e2p40r", "Integrity": "NYtj6I7LAHqROHO89xC/4Ep/PO3X/ZZtKeXl1sJHiJc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-grid.min.js", "FileLength": 63688, "LastWriteTime": "2025-07-10T11:22:49.7638578+00:00"}, "vtckrpTV8b6zSzQ1neIdfxajgv27Fx7+RTfCsFlL89g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\82nczmsris-w366y88iin.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-heatmap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-heatmap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v1ql1ejuyd", "Integrity": "3T+SFs5AJZLQN7x4hf9UJo6Ze4+/4x6yhJ2r83ZP/5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-heatmap.min.js", "FileLength": 5427, "LastWriteTime": "2025-07-10T11:22:49.7660879+00:00"}, "mcNW7pKqz6ftreHgJEItc8zl9t2VOovY4icLECCT2io=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\jjmy76blcj-8bfu2omx3l.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-image-editor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-image-editor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "krk8hhzys2", "Integrity": "4J858GTUVh/5tvUV9ZuTOmoC42UXPcsaxkc0PheJjkA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-image-editor.min.js", "FileLength": 155039, "LastWriteTime": "2025-07-10T11:22:49.7954215+00:00"}, "7MWw9KOKFLfbSJCujLpEs0GPYnv27k12bTra8WNnK5A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\vlup8812vg-qu6rzn4nuc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-inplaceeditor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-inplaceeditor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1zotwj2tf6", "Integrity": "UzBKXFL3D0iO9hF/0FJhbOrnkXBb8trmgXbOmOIZe5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-inplaceeditor.min.js", "FileLength": 2201, "LastWriteTime": "2025-07-10T11:22:49.798783+00:00"}, "N7GpadcqzEPWx7OuqYLoRuDy1CCcPxYvcEd1FoWaSqc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\vjes1wfbht-3lors5m5qf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-kanban.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-kanban.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sr1rya3dl2", "Integrity": "SITX/IXfyS28e6O2SKLhO7YHhYFJiI69cDMuRyntBn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-kanban.min.js", "FileLength": 9546, "LastWriteTime": "2025-07-10T11:22:49.8043473+00:00"}, "RsBrwXtLFRxE4ISuTmK3PttoNYK6GHAJFiSPHFeVq2k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\cjlbdrwbmy-oevwmbdxds.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-lineargauge.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-lineargauge.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ob1p17roi9", "Integrity": "9J9oR5BA/yBAmiwjwITYcyUvP4ek/Cews2SKXrbTDPU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-lineargauge.min.js", "FileLength": 5195, "LastWriteTime": "2025-07-10T11:22:49.640069+00:00"}, "JEjDGCQTB/y82CPZAEC6xwCLMTsR5VWL2htIW0xI8YU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\hpxslzcfcx-lnakauwyr0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-listbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-listbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5sep8l742y", "Integrity": "FQPrriNLSVRP6HaTLT+Vgy88ZyYkCCz7xU+CNlE402E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-listbox.min.js", "FileLength": 2117, "LastWriteTime": "2025-07-10T11:22:49.6432053+00:00"}, "te2YGxtoste/Znxaww1P13ZPk2Eatqd66rAUPb2Y460=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\czt8ptpkp9-e73iwxhd6a.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-listview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-listview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yzt53ptahy", "Integrity": "Hl5n/zwU6+htwaI9xbjNZ0/7jhRHMIjbL/e+/mfwtD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-listview.min.js", "FileLength": 5400, "LastWriteTime": "2025-07-10T11:22:49.6452049+00:00"}, "U0AvdjpVDV5Jzenc+5GlBkHn6MACBx8Rbwz0xvJJ7DM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\7s51rvoei9-nruwlw8ll3.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-maps.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-maps.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f9bxn11xsn", "Integrity": "QyJNounohLJqyTulByRTFgmpekhFNtg/G0sVmIV/joY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-maps.min.js", "FileLength": 29120, "LastWriteTime": "2025-07-10T11:22:49.6536053+00:00"}, "CYI3p8C2bGCgGVVhNDj/JhvEyLJb2Yz3kdLHdAKkTKo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\iukitgqwf9-ttsd40vq2j.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-maskedtextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kguzy40vse", "Integrity": "DMmmifBX5CS8f/dr6rYLRFcEONFtOPiWQ0ve/gdAvhU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "FileLength": 2788, "LastWriteTime": "2025-07-10T11:22:49.6607312+00:00"}, "6aNl501zaC+olanKcBqBbXl3SahugG8aZqcXli4thXE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\be1fr8o4r3-hc7q9x42f9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-mention.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-mention.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wkiax03grh", "Integrity": "kG1lgzfq98U4kg0JeDBFbbQUem6jgmN1PRPVYg7mjUo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-mention.min.js", "FileLength": 5959, "LastWriteTime": "2025-07-10T11:22:49.6658197+00:00"}, "SceEavuCUMYO0TzkqRyD+z/D69HIfjQtWdBX5OR5JJc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\gtnoncceba-k4aq3j8y4j.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-menu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-menu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q8hfdxjzxr", "Integrity": "CkalRVCOU4ws94mOdqOR95lzyxbtt9u1c1u1aomA/j8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-menu.min.js", "FileLength": 4105, "LastWriteTime": "2025-07-10T11:22:49.674011+00:00"}, "kXc2GQEuY7nN5lUkRRhj9snF62eSorSMdgk6ADhRA8w=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\lkespo2mpc-5oxk42k5we.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-multicolumncombobox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-multicolumncombobox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hfhp3qjx41", "Integrity": "g+C/IWijMUfCtym4i7prpZ2fDBQ8+Q3jAwNwNSSIuFQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-multicolumncombobox.min.js", "FileLength": 2337, "LastWriteTime": "2025-07-10T11:22:49.6755088+00:00"}, "lIBgj5ykHj0SOa/ABZYOzICkxx1v3MDSmMFujlyCXjY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\xzqibnkirt-6vb6a8tlna.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-multiselect.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-multiselect.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "91ebvvy7ps", "Integrity": "M6k2yFqkzNCF8R3u2uhBhi6KdY2htXqD1VzP4eciwPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-multiselect.min.js", "FileLength": 8701, "LastWriteTime": "2025-07-10T11:22:49.6800839+00:00"}, "TX/rzyexME3HjKikdci2MO5oiI3UIxpB9DimsVPlzP0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\itlbf61hm6-74t8isjgng.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-numerictextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r5g2hztll5", "Integrity": "T+tSsRbkC/Vw4LIiFowH7cCP0pLu4gFMSjUSUaFU9Ug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "FileLength": 3255, "LastWriteTime": "2025-07-10T11:22:49.6820834+00:00"}, "V4cAZskN9B3iCyJMIWSHQiooi21Ez6clXopK+RuQpSI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\s64mkyknem-lxthaukcgm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-otp-input.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-otp-input.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "154kl9zutj", "Integrity": "CwX0Fx+skbK7P048NYzj5eCgSFs2xjjg3tGmrlcHGW4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-otp-input.min.js", "FileLength": 1158, "LastWriteTime": "2025-07-10T11:22:49.6830848+00:00"}, "Q1/sk8iap0DMvcxCGGG3j/dAlhYDOqfQRkXcz0k9EMQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\uofcb8dzuy-v3s6ia66t5.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-pager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-pager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m331054lmr", "Integrity": "DobwQBHybHCqOADQXv7+pDdfOmpbKoteBA4HdT0BHHc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-pager.min.js", "FileLength": 2610, "LastWriteTime": "2025-07-10T11:22:49.6850855+00:00"}, "5f6kpRlPWcL9Hcic6qU+Wt1TNBg+E4dFshIfVOHZFtQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\dkkcf86snh-npjbxd1o9z.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-pivotview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-pivotview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ehrrmqs94r", "Integrity": "Hr492Rn97B6RXZmm3E2wrgY5wpCmjXsUwSaEEKYzU6k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-pivotview.min.js", "FileLength": 20131, "LastWriteTime": "2025-07-10T11:22:49.6948563+00:00"}, "WAsqCr6aAG26rWK8tloYPeaQ6+clHvoS19a7LYhcZ90=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\apf3r30luq-mu6w6v0370.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-progressbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-progressbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "odpb2km2vs", "Integrity": "PEeXWnUNsJJhEiwMS3FOcPDH58ipA/WtPGErRcSImxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-progressbar.min.js", "FileLength": 3862, "LastWriteTime": "2025-07-10T11:22:49.69696+00:00"}, "vQW4yAUC/SiTUqR/qZIZwYj5jNNsL3WUqGEy37aQn/o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\9h9zfn7yek-90tcmd45kc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-querybuilder.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-querybuilder.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dn682uqr60", "Integrity": "qa0mDLWdQedGDDXGdVYqxEtsy4gR45AMgLqD0dGAv8I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-querybuilder.min.js", "FileLength": 2035, "LastWriteTime": "2025-07-10T11:22:49.7005778+00:00"}, "jzWXCxUFJyA70EHghHAVBW+8kFhRuNUwYMMn4mtItos=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\jcgtm963n0-6dn46uzeo4.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-range-navigator.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-range-navigator.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "su4kiu36ko", "Integrity": "lI9huZZxeTUCRrPZp9HvwtNfeiy6RD0rre4z9DKeFug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-range-navigator.min.js", "FileLength": 4430, "LastWriteTime": "2025-07-10T11:22:49.7126583+00:00"}, "aYvcC0euun5ZzCDGTUIJmKoGT7mIT/Nv0DxsnzM17us=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6xby58i1ne-uhuze8diue.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-rating.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-rating.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yk61rdz93f", "Integrity": "kpa9Vfg9+Wh6NKx7Tv7DHw8zVSNDhYnV1uPa7S6pevE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-rating.min.js", "FileLength": 2493, "LastWriteTime": "2025-07-10T11:22:49.7193937+00:00"}, "H4aE8RmwZkKaCgKxc9lZcAClMWgzVophMivcN3xC/So=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\5qjtvvhsgz-9nt3c9ua1v.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-ribbon.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-ribbon.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "64j9m7idxl", "Integrity": "BnuStfS5tpc/zc3D+zp2U/jXvjiau63KKUNj3WJLDlU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-ribbon.min.js", "FileLength": 15823, "LastWriteTime": "2025-07-10T11:22:49.7244546+00:00"}, "VGZn+oI9uxMbwKhcnm9Uz17wK4ZbHw2s4QdEwi0rAFY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\16wg0iqttf-641w5d5d6y.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-richtexteditor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-richtexteditor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vpxh0n4vni", "Integrity": "DX+5SyPqsqi/l8QVaG3O3ht3O0N5d3JA1khVEBWxExg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-richtexteditor.min.js", "FileLength": 198776, "LastWriteTime": "2025-07-10T11:22:49.7625286+00:00"}, "dEpVWPhYrhMHnbWEH5gQvc7Ae2uEGN9/cclJIh/loxk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\qxbdc57p9r-jjk3v2jvgz.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sankey.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-sankey.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ua93llfavr", "Integrity": "KpV4Ex4Co9au/NMVaVA3sEzDWWEL5I7LwYDWul/HUsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-sankey.min.js", "FileLength": 3339, "LastWriteTime": "2025-07-10T11:22:49.7660879+00:00"}, "t91rz6/pgWWxA7DBdohH8NvYzoM/2enzn6zsSC0t2bs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\3a4nupymxx-uqehqwrbea.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-schedule.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-schedule.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r8bfhvqljb", "Integrity": "Vaw19eUV/ZC2uifbLbBlhoQ7jI+8LSAzjbX007J7BKk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-schedule.min.js", "FileLength": 51054, "LastWriteTime": "2025-07-10T11:22:49.7764957+00:00"}, "MNs146+0ZuKIHnEwqBuA7SvWZDqS8bc337CRlig/cWs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\42ck71d2wv-y0p3olwrvi.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sidebar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-sidebar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dyce9401qt", "Integrity": "FbJXHpZDKYBSUCRaXbsQS88ZIDAka2JZoMzdpawihZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-sidebar.min.js", "FileLength": 2967, "LastWriteTime": "2025-07-10T11:22:49.779669+00:00"}, "5TH+ZKLUHoQqTQQbzyCdHxjUh4WMKilxtSeI4qdtZuw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\hruzog0nao-kwmqgvmftt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-signature.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-signature.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ru0hy8euqt", "Integrity": "bf/KiddwPq+iIGv6p4HgPWffOe6evE6D1Gm2TGwLeoU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-signature.min.js", "FileLength": 5967, "LastWriteTime": "2025-07-10T11:22:49.7820414+00:00"}, "d/JSZopHRJfn2sO+wnnw7RntaDxyRqSM9MZaBjicYc4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\8z7q0tcoh7-4bw7klzqo8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-slider.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-slider.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rldfo89ix0", "Integrity": "nzrgdlOab6NA/27wxtlDHCn6xcjUlApSqLsdcMyDYw0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-slider.min.js", "FileLength": 6450, "LastWriteTime": "2025-07-10T11:22:49.7841548+00:00"}, "qxLfPqv37HsnPoj5vSGqqy5C0nvf1s3jltkIIBIM2VM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\r4s30r6yiw-p64x1ir2t2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-smith-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-smith-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gsf2b31sff", "Integrity": "kFxL8wmEEUW5DFrwWISRGepLoAn4GLTjCcAw5fzcd/k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-smith-chart.min.js", "FileLength": 4090, "LastWriteTime": "2025-07-10T11:22:49.7888001+00:00"}, "/VarKs1rqkm7KopMANEwzyCB3phvAiE9fNiBYyR3avo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6jncl9y7mi-y52tvs0d0d.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sparkline.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-sparkline.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0sh907mxrp", "Integrity": "QN5B6Ls9Qtp57OC7b2yKvfsDhM0bp/vvxFl0f6zDXhg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-sparkline.min.js", "FileLength": 2409, "LastWriteTime": "2025-07-10T11:22:49.7944217+00:00"}, "NDAFycU2XhOiG6n5TdxFEzxgi/ZLNotUK0gWQtezwfQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\lsawkhetsb-syppfg95vn.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-speechtotext.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-speechtotext.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7992l0y3y1", "Integrity": "W7CM4/3RB2VFl0+wh7punKGVaeS1WXWcKeKWH2rcJEw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-speechtotext.min.js", "FileLength": 1116, "LastWriteTime": "2025-07-10T11:22:49.7967837+00:00"}, "AsP0OlRFxTlubn2ilXHOzcYAqrEqR4n8fzN48cZKKsQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\3ex2m3if29-i09jk3549c.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-speeddial.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-speeddial.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xjkgoph2gk", "Integrity": "DXgZoLOw6XUMFVp5VlCjgk876sNNdLYJHK2GjjAKOYo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-speeddial.min.js", "FileLength": 2744, "LastWriteTime": "2025-07-10T11:22:49.798783+00:00"}, "QOX3IgcXLK7kLzVdJxxzjfJ8eYuldcRJHecMjvIxiTQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\0rac66dpmt-zwk9e509v1.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a3k4dglwg6", "Integrity": "SgQ5ea6aMqftmIcacRMZQAsK8RRrZgGIuAV5H5v9fp0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-spinner.min.js", "FileLength": 676, "LastWriteTime": "2025-07-10T11:22:49.6390448+00:00"}, "LytrrN399WyZRk/DlDakupcfi43DNX9thNBFPpRtFhQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\aadd9iinhj-4ti3vm3n05.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-splitter.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-splitter.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pawxarphwj", "Integrity": "wYdeCxTAG5EMieBLcXGBLuiRqeNJBfWOQ+q9eiR/y2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-splitter.min.js", "FileLength": 9478, "LastWriteTime": "2025-07-10T11:22:49.6432053+00:00"}, "7zB2xiMwSkkWeIb+3Zm8yUeHxXQywQ7D/Ogm5NhVbAc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\nxqjd904l0-1it6se9g9h.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-stepper.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-stepper.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lktrkajs6o", "Integrity": "iwWuBPT7nNHxATldCjmolYU2k2Rnz6ldGunIiA2IVvg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-stepper.min.js", "FileLength": 3578, "LastWriteTime": "2025-07-10T11:22:49.6462062+00:00"}, "o92EHmmxcHrKAu/Zld0ocMnaejqJEkdlF1kNPOywIWY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\5esrip7fz2-t0jhcz5g73.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-stock-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-stock-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1v0pj5mp8s", "Integrity": "nZfVd3D1SlBBWwZrT2MbWHmRWWMGeWRRdcVzFTKemSs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-stock-chart.min.js", "FileLength": 3671, "LastWriteTime": "2025-07-10T11:22:49.6484169+00:00"}, "FElGkzKNI+Hu9DUcuEAgL1bN0/kvOKGtOrncfRjqpCc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\zr19gib3w2-hcyhmoriqg.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-svg-export.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-svg-export.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yp8pad2a4a", "Integrity": "ZjYlfxj/KG9t5CAIHFcyuXiG/qfiTl/Pf4sbBqFjK8s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-svg-export.min.js", "FileLength": 3884, "LastWriteTime": "2025-07-10T11:22:49.6504339+00:00"}, "X2KtTAOI9QRFJ7YH1YLdhOGvsbRkER3rwp7fMWaa5gc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\b3svqv6skf-e6ikmuz3kr.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-tab.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-tab.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g91n8p4j2o", "Integrity": "3I3LuHc96vnmwf1qKMUwLn+Mdxsp9r7wFHQFltf8GmQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-tab.min.js", "FileLength": 7890, "LastWriteTime": "2025-07-10T11:22:49.6526064+00:00"}, "Chqx15cb/7drbb1zXTRheamvk36S3cDU+qcVLln3Gco=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\t9ldm31sr4-wnihqal9eb.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-textarea.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-textarea.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zfla0uh7jv", "Integrity": "D5Mp37VyXl9YQvjFyscfIB+0UCC5xV49asQ7tSDZ0KI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-textarea.min.js", "FileLength": 810, "LastWriteTime": "2025-07-10T11:22:49.6566061+00:00"}, "008TsYtL6oeWOTYeZ2edyd1lwPw5Jo/0LOvNROQtgm0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\5sm0ch5rs2-jnv0btyjwc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-textbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-textbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7l23bodg73", "Integrity": "20qWHqcZxlZX5zzIWd42RTSAQdWgkkxlYKYOEbj4+4M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-textbox.min.js", "FileLength": 1295, "LastWriteTime": "2025-07-10T11:22:49.6576057+00:00"}, "NbUk6VGM+AMwdUlWSMgxy5dCN1J5nqdHDMA2cZIReT0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\8u9jrxeb4z-bkdfhu3k7o.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-timepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-timepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "889cce2ow8", "Integrity": "uF1F6WPa4FQlmTmM082AAIPQYiQXRRCIcfEmDfPOxcU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-timepicker.min.js", "FileLength": 7693, "LastWriteTime": "2025-07-10T11:22:49.6607312+00:00"}, "dfGlsTtKxmVHogB8b9atxnHf/+/XFC8Jhcl7d9X9ndo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\qr1eqiq81d-dj9bppcrp7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-toast.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-toast.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t0aipz9uoy", "Integrity": "PTgl5KlemSVA7OfrWk76p1Q91u81vch+N8yWPjFhW40=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-toast.min.js", "FileLength": 2429, "LastWriteTime": "2025-07-10T11:22:49.6669594+00:00"}, "mpEnCid1NjI2DDlM0SpjDzejR8kb/UxcXK9UtXnx1Lc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\qeg02v8nug-jp5es9g2xz.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-toolbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-toolbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ta7bvi34c4", "Integrity": "S88rxnKcFOHmA/Vp1FQhOl3T/9ACpke3+draaaZGR4s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-toolbar.min.js", "FileLength": 9542, "LastWriteTime": "2025-07-10T11:22:49.6699604+00:00"}, "MoOSKS6Q7AR5ltjV1LfEYGXPRvmgwUx/aCkUClkX2as=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6iv2gqi52w-ynodchr4ll.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-tooltip.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-tooltip.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yfeq60ifui", "Integrity": "Yo746svIGc2I2yzNaqPdu8vmXCbuJyNvqp20ZziCXxk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-tooltip.min.js", "FileLength": 7502, "LastWriteTime": "2025-07-10T11:22:49.674011+00:00"}, "pDba0uxoh3SAgPg5NRyW7vdCD2VueTkEmwJLXptP2kY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\d8h16lm625-rtxbxltgt9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treegrid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-treegrid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n2t3hxjeo4", "Integrity": "7zqTdeaI0ft5B/hOmZVvYYhDyU4iy+KG7Ij9e/48PSw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-treegrid.min.js", "FileLength": 7691, "LastWriteTime": "2025-07-10T11:22:49.6769451+00:00"}, "aWGD6eBZBkYjvH4uBnSBZ1Gzh2D6xFLqqcprXiNDQsg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\tuovec8evf-i4mr24r7r6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treemap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-treemap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "40kq99579n", "Integrity": "PJvym0VvCL3HLPBDj6CxGUW27ty2ANF/mvw7vWvPcZg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-treemap.min.js", "FileLength": 2920, "LastWriteTime": "2025-07-10T11:22:49.6790837+00:00"}, "mFAr8fOTaUIEAGY18UZE0SSVWc5mZ8g4zbg6eByhsSE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\1ad0wz8wh3-9kjkdk31rh.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treeview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-treeview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2pfwnzemri", "Integrity": "iqVXr57GfVPuJV1DFSIPozvWuPvx6hom4BkxA1sHI3o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-treeview.min.js", "FileLength": 12162, "LastWriteTime": "2025-07-10T11:22:49.684085+00:00"}, "FCGATOtTGuwtGmitQfjOJIzKdZkCsfVmjMnblHvb1pg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\j3374ns99t-la35tghzbu.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-uploader.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-uploader.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eozm84mzpy", "Integrity": "HhYn6WawfId9Chkcd7Q9jTTVjR030gM0KygkNpLNEvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sf-uploader.min.js", "FileLength": 19275, "LastWriteTime": "2025-07-10T11:22:49.6925894+00:00"}, "yE2ExgMHdUZMXBfGejx1MJXiT5yVdum8CTh6zK4jt+k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ybfh3feh32-gz6e3atemk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sortable.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sortable.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qg9tz88jr6", "Integrity": "1seqLGN+uZXKebkOzmIr1OWtQvDvfXC8FuBGBxtItZ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\sortable.min.js", "FileLength": 3144, "LastWriteTime": "2025-07-10T11:22:49.6948563+00:00"}, "jl8/GKE3lCJFs8dd9/T0901RsRkOkxVNzVqUBzEbqhk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\0b5pm8t2xd-ap0tuxedpx.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bk80ui66op", "Integrity": "W3HMht9TsmsDsoWokgCXrF94kmFbSc12rUfa9suBrXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\spinner.min.js", "FileLength": 3379, "LastWriteTime": "2025-07-10T11:22:49.6994544+00:00"}, "TVtnWKGzcqVMcAr8EHjXVuhyFmKn/XrxbE7iqAYhuFI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ancwpmpcu5-4dvlhbz2ty.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/splitbuttonsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wf2dd5ojps", "Integrity": "nO7McXVmulgxW9K7x9r1ej7hmr9nOxiUcdGnPa+/oHI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "FileLength": 1445, "LastWriteTime": "2025-07-10T11:22:49.7026025+00:00"}, "R+X4+m6+mqTsFqZsG+u/lznpCALxgSqbz7KV0Z4UXpU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\spxlaub6dv-372y316ixj.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/svgbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\svgbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9cp8ip045g", "Integrity": "SYirkJzFZFs566qSnGNG+dW/a/uo3r7C/nhpQ13BmK8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\svgbase.min.js", "FileLength": 12774, "LastWriteTime": "2025-07-10T11:22:49.7086437+00:00"}, "XNyZOxeXdJTJMGx9HPgBmBmo5Qtjx0eNjfBkMnJQ6Gc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\tt93irvsj4-6hnvg0gilo.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/syncfusion-blazor-base.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1rwl13hix", "Integrity": "H2YuwkY6j5ajoWjVOyedq5xhuq/xMlhxkzGdyNFVgo0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "FileLength": 101902, "LastWriteTime": "2025-07-10T11:22:49.7216304+00:00"}, "1OJcDGPwaTgHbunlT5WoFmnft5xWCBoyvtUnkmwlhPE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\bqvff7ljya-of9mv9oy6q.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/syncfusion-blazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7dhjes8rm4", "Integrity": "VSOy8S7pHf7tq2741sKxA5fSmNjF31TrqQ50MI50mko=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "FileLength": 989932, "LastWriteTime": "2025-07-10T11:22:49.9263043+00:00"}, "jpRPn63O7jC6qXn+R9guWK/euM5gAY/2aTMR0h5UlFE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\yvsznq4373-whph9uh8dy.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8jofbi9ww", "Integrity": "ka3JlDStzfWtIK6W3SJzGcPFjBUdQ5akm3yIZPJIxtw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap.css", "FileLength": 474322, "LastWriteTime": "2025-07-10T11:22:50.0161551+00:00"}, "0F+3qSpLt/vPc5PNWONWp9MYuIE0mBznE4FVIxWTk6M=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\xo1my5bok1-i3kjql666c.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qd3gbhwiqw", "Integrity": "4jupm/4+2SWkpHKOaRxXTktjhwzIrI3ITHKRJAi3DD8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap-dark.css", "FileLength": 473847, "LastWriteTime": "2025-07-10T11:22:50.1023123+00:00"}, "Fko1jZUJan3d2j3EotzZL9eIr11/+9Px7MDCYs0AKTg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\86a35pd8i9-1x110r870b.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "70pj9jfzzj", "Integrity": "qXW9D2xqb0+9nXEwrPB7Tgy2ZU3L2Yf4YFEAUY0yJFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap-dark-lite.css", "FileLength": 473861, "LastWriteTime": "2025-07-10T11:22:50.1916129+00:00"}, "5U+/J+lQWm3Z8x1mf8LLpbEER0PUKgy4sLBdoJoS13A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\8503zsol33-o4xbqlpium.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rtnhhgtg9a", "Integrity": "31PUzmB2xr487tdkV4g8OcBjTUaXrYuIsL2puwmAtDY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap-lite.css", "FileLength": 474324, "LastWriteTime": "2025-07-10T11:22:50.3373336+00:00"}, "mxgaPcIX6wbvWM5xnbT7c54QcK1T0LO7RfJJbjczeN0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\34yiq18zxz-utj2dchh0h.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap4.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap4.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wc73l990ls", "Integrity": "AC83p3Txj5G0YXdeYkeygfGaRW3EUg1ouovd30G+X28=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap4.css", "FileLength": 464620, "LastWriteTime": "2025-07-10T11:22:50.5144087+00:00"}, "M1mzKMAEAj2Y9k+iQfZQwfCDhX0yVFbsqz3/KtH0Nr0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\rugulc3dbo-yhdvff49gt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap4-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap4-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m61uw7hr8c", "Integrity": "gjKTcro4kfDqIPz50Q+nqDtJHMe6/fpO8VLjKfCcHMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap4-lite.css", "FileLength": 464630, "LastWriteTime": "2025-07-10T11:22:50.6533653+00:00"}, "2oCfEDmJOwTyyhnxDeSqOs+7qPj/po5WTgPH3dybk9Q=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\rt6g31qov9-94crdf3e51.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uh5ba0yvnb", "Integrity": "buW6JdcGODqsHoptvJmAzrOImagrz9FgXjSGwISTssA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.css", "FileLength": 472071, "LastWriteTime": "2025-07-10T11:22:49.7515236+00:00"}, "dPx5s9kn389AUJMQWm5moBnUP62QzEoOe/n05CmYfP4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\16lpm8pp05-3rgcbimoyt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ix8d55haq1", "Integrity": "dlcLtuKVJ40xOJxk3lmIe9oxl/t30AFqCPhkSh2bUY0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5-dark.css", "FileLength": 476207, "LastWriteTime": "2025-07-10T11:22:49.8543463+00:00"}, "ge4YilmR4NfRtddI2Uv7TG9rXf1g9lpr4lfmGlTMXfU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\7hpqi5txla-xlrgv2ppux.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qni8cs3zq3", "Integrity": "dnzpz/0ALKwPDBBWQiCVjVtMw3vQqYjzI+nG+XWOsyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5-dark-lite.css", "FileLength": 476197, "LastWriteTime": "2025-07-10T11:22:49.960203+00:00"}, "9IrioVTsXwU1oTDFu1nJJiFzeAiHzy5rPXsF/l4fC84=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\apybc81neu-4c83uxrswk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "by2ulxp2yz", "Integrity": "jSz+MEOCYR6I1JDvXy98oA4pxamcRZqO1l7YCd7WWFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5-lite.css", "FileLength": 472072, "LastWriteTime": "2025-07-10T11:22:49.8974187+00:00"}, "ziupBqiwA66uKBwjaCh1Eu1MtJv9tCzwoK/yNznrZCM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\uoq7cj833o-9b8xedne3p.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n8y7n8n28u", "Integrity": "6c+QCa2C/UiJVwL0gIE9kJeCR3v74SY1vQUXF+Vcr34=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.3.css", "FileLength": 477154, "LastWriteTime": "2025-07-10T11:22:50.0089506+00:00"}, "5aoF+8nbkRKtrImoipOPbPYQd+Tn+NXTYQqK2Nby2jI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\0ljcaj2py1-i6bp60k3fk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8mcl2wxbv4", "Integrity": "4Dp8R/7VEYCMAdRnYJGT3eKWJehh05mFIYIcEtijBJs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.3-dark.css", "FileLength": 476295, "LastWriteTime": "2025-07-10T11:22:50.1293642+00:00"}, "n87sY0FH1MzaPf6uEG+hhR1y/WTrVPjIqVKII0ZoLmk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\09tjyrmpyg-cqejuz8qps.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o3pwpoeoo6", "Integrity": "7L/v97DFvwLWp0YTcWBWJF/6BYztdCXyjNmtEkVrX1U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.3-dark-lite.css", "FileLength": 476300, "LastWriteTime": "2025-07-10T11:22:50.2505615+00:00"}, "WtAuY2lzNSGhAMdIaEteoJ8XgRfXDroTlBm6g6CUqOg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\r3cgco6llb-fpxrc2qkh2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9pale0b34d", "Integrity": "k5hxQ2CAAb+N7Qd5H/bVJVnfqJo6HlziOwAYzRy/lUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\bootstrap5.3-lite.css", "FileLength": 477157, "LastWriteTime": "2025-07-10T11:22:50.4186653+00:00"}, "IkdunJFAOpbB5Qi7Oh1bQgHRzCXxtgHlLofLEOJCY4g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\3uil544spb-titr0y3e6c.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\customized\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ni3x0c4sn2", "Integrity": "D4AhdhgCeAVY0fofDZAeYk2jE0ZdsF0bJG50lYu+noA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\customized\\material.css", "FileLength": 502126, "LastWriteTime": "2025-07-10T11:22:50.6025037+00:00"}, "wYwtZDQYdiL3Qw4LCAg/z+54ShUcx4+S8QDOpNfrnOk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\0z85kndwvu-fg3l2q9ila.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\customized\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "71n9k0ij4o", "Integrity": "Ja+WC1K50xPJEsVTjUWWILW/OJNhX2qqFsA60aKgfjY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\customized\\material-dark.css", "FileLength": 510071, "LastWriteTime": "2025-07-10T11:22:50.7169381+00:00"}, "CouBgU5N6guNhu3mLrJwCVpgi5sN04ryaHVSorMroZU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\bxxz62r4fh-qr94prjpan.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\customized\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5uu5wqekx1", "Integrity": "XdNxu/KbTm8YM5/mariyYazRvSU8h8A5jEJoqg7Rbgs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\customized\\tailwind.css", "FileLength": 436028, "LastWriteTime": "2025-07-10T11:22:50.8042346+00:00"}, "eUzHELgZXlPw2v7xumYdYxz4DNMCuLU1466zWWUc5EQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\97jndmeew1-vqaa9nckmz.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\customized\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "utodxg6v7z", "Integrity": "DiAnJqOsWByQsHOXQ1QpAGCtOU635rzTV6L0YEVaJG0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\customized\\tailwind-dark.css", "FileLength": 438894, "LastWriteTime": "2025-07-10T11:22:50.8853605+00:00"}, "rPEh6ils3f8ubRv4zlkAabSVbYuQkIRJuV39V7E03ZY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\tkqxgce19s-9mnlgchzvl.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fabric.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0jph15z16l", "Integrity": "fOBCjIpKdiNZEkUjRlU1A6TRgwePxnxJii1RnqpwP+Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fabric.css", "FileLength": 453046, "LastWriteTime": "2025-07-10T11:22:50.9520817+00:00"}, "dxg0GjLPsGfA91+DNhjQlqjnSQX3hSu3J+1auNz9dBM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\giwg4o6lvn-vt4bu1zauf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fabric-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5jzg7f5b2p", "Integrity": "H9VCL5zYvkeVXEORBmQ8XYK18I5BHLFXMLVAh7SKEbw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fabric-dark.css", "FileLength": 462339, "LastWriteTime": "2025-07-10T11:22:51.0177249+00:00"}, "taiDG7QtUGabGA1IKfBxeDcEpjyF8sgpvD14FgScFIE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\9exgy2h7v5-qs201mq4gk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fabric-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "avn3n2kc2r", "Integrity": "lpqrJnDIKY85SfNzRia0so5OdlWIrIq5Bgyux1OUid8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fabric-dark-lite.css", "FileLength": 462336, "LastWriteTime": "2025-07-10T11:22:51.0775206+00:00"}, "zd5kqnPkv2XQGn2NuGmUH9VqSaFY18Xy6xJcf7hw79I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\0roaiwm1eq-i7agl5qmgk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fabric-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t34kpkv7q8", "Integrity": "yYrk0PVbj1AbOekyQ57DHc+HuagPfPNBa3eeOSUT8Mg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fabric-lite.css", "FileLength": 453056, "LastWriteTime": "2025-07-10T11:22:51.144626+00:00"}, "Ivdcjos3XngQPRFozhW471tWZ677xd1aX+z+JnJ4/TA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\cpd57ko01h-o7ot2a0c1i.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uyebfcj28v", "Integrity": "hu+WiJHaGZAPAkcd6VdHwAeRnQui/TAV5qLyDLReXOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent.css", "FileLength": 451807, "LastWriteTime": "2025-07-10T11:22:51.2064192+00:00"}, "hK/p+RPxYAD8tyDWqJYzR6uzniZzQ8Btaj1Eka/RBWM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\18b0yyeh6j-hb2rzx77hp.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "32cq4o2p5n", "Integrity": "dfzz5Q3Uqwarqh0We9GXmh18oB3K4WG4tbaX/9VSpfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent-dark.css", "FileLength": 452216, "LastWriteTime": "2025-07-10T11:22:51.2644561+00:00"}, "PJZGOo3JTaAlZ4cf3dFS0Uhoqi9+EftA5ntWSDKgHBQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\r5l6zmsmwz-00jrbgoo05.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4r83r04c46", "Integrity": "8HXms9XzXdfnwzFEYNXaIZZFfCAhj8t0KZt+VZwDg9Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent-dark-lite.css", "FileLength": 452221, "LastWriteTime": "2025-07-10T11:22:51.3405446+00:00"}, "10ow9hTMzBOmGUn/tmPuJO5N62NlMHEaWEDymlbZQco=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\w0f18brd7i-65l9kxo85p.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tggpm4j6ii", "Integrity": "nTUOGACr4hZt9nxGR+q+JVjCql8uaQNPA1vJiiqcvXQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent-lite.css", "FileLength": 451811, "LastWriteTime": "2025-07-10T11:22:49.9047579+00:00"}, "aUeaUgfgKZ3zV/n8NjlXRVIgZlTXdnisHTSGG1oawZ8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\tzmdu8q96u-1ykyrlysvi.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hrrxqa1szz", "Integrity": "zdxJnjRbIq9l9c33I0sykFbTxFDMoj2TJYdJIPY0CBk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2.css", "FileLength": 486257, "LastWriteTime": "2025-07-10T11:22:50.0226654+00:00"}, "eqX8qWBz6uPV1b0rTMVos8q0P90kSoajsrWo/aL+ZIk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\azz7n0gu3n-82rmm1dtqn.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1j05b1cjsx", "Integrity": "7aCrQSiEeO1UYUjN5v4Qz9eea34ThDdzgwqi1mIgej4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-dark.css", "FileLength": 483320, "LastWriteTime": "2025-07-10T11:22:50.1467151+00:00"}, "DwAO56Q6Z7dwoyDaZG5Afue2ZJhiUn1oW932CjnCGDY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\bu4pbrast1-5yzz4za2su.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nuw37omp1t", "Integrity": "8c+Vb4AL4lT5ZCzS2LWQW1d7sUcBBcAHQih6EHj0JhU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-dark-lite.css", "FileLength": 483329, "LastWriteTime": "2025-07-10T11:22:50.2696049+00:00"}, "UeZe06AdaUWgc7XZP0eTySt0otjm8im6ZrQ4PG02GGk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\80ppwlqd2m-351ns652dd.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hm1z2mln2y", "Integrity": "B6Or+XL2OfPYzm7Tl4DtpgQ8fTYds48CYO6p4HUPfA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-highcontrast.css", "FileLength": 484817, "LastWriteTime": "2025-07-10T11:22:50.4449691+00:00"}, "f8DJkjihauEsGoamPw/DwjlNcn1JDuit7Bg/6NmmzXU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\dazefiugh9-ok92gnhgn2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2x5zpxhgi4", "Integrity": "SgqBKK8OLrLs0RdiOYfjo2hNF/jujmG0ZNKQBE+zIxI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-highcontrast-lite.css", "FileLength": 484817, "LastWriteTime": "2025-07-10T11:22:50.6219893+00:00"}, "/XwpohOz5GoQW5ms7+863uv54vylUslGCMD3Cd2tbwA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\2lur8dmyl3-ofe2zo928b.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uugha45443", "Integrity": "lpl7rjKHiFRIIaTOGQtTXBnfDMctporwKuUgfFDia3I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\fluent2-lite.css", "FileLength": 486262, "LastWriteTime": "2025-07-10T11:22:50.7372458+00:00"}, "YLfcGjrKnQy5fmraxrJTU8T1d/scmiYYUyUV1KImHXY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\wb6pcilptw-cltxiimpan.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yr7s32exc4", "Integrity": "XmH70XnHCRda33sNf05W4huzIAvowXhRD7xhG0aChHA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\highcontrast.css", "FileLength": 451398, "LastWriteTime": "2025-07-10T11:22:50.8254+00:00"}, "VMVuaHVTe5gEzCjRDTNyhDSrt1Sbzzn8KxSPESAbD98=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\jnn0t5sikh-jyx5tqf1wt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "326n5bh7gx", "Integrity": "lYDSrkQO9z6AYvVCQlmVh3MqcJTg6dcHM5YhecXTlEQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\highcontrast-lite.css", "FileLength": 451415, "LastWriteTime": "2025-07-10T11:22:50.8997606+00:00"}, "SFNaywPbAAikcu6bbfGQECcjLgyCB1G5YqQ9NvpqPZY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\tz16aznt9x-qb8mqdc59t.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o954d64cra", "Integrity": "VMcldbJqd/3VV+fJDlpC/UqjYL01kpwYXtd/UeMMWHc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material.css", "FileLength": 502165, "LastWriteTime": "2025-07-10T11:22:49.7540073+00:00"}, "JWDPt4aHodtlOEeQALarD660d19YP+ikFui/5gbptko=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\fr3spr8ava-1cbp8x3s91.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hdedp8qhax", "Integrity": "BUPX4b64KIsdh82KRe8MiVhvj9B+i60GwqwaSyBeq6k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material-dark.css", "FileLength": 510073, "LastWriteTime": "2025-07-10T11:22:49.909385+00:00"}, "QZrE301TM0CwPMAlKMb7+PSmgjgH/2m4gcX1nDGYebs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\vfpw31d6oc-bkwqcdta32.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ebv1j0x7v", "Integrity": "aFgTGmlFuKcYum86sYMfX5IoI0ZLCrPjQr9CPNVDOas=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material-dark-lite.css", "FileLength": 510074, "LastWriteTime": "2025-07-10T11:22:50.018156+00:00"}, "pAMjqROHU79Y4gtNc8CXVMZmSjYkLYWUA4OW4i8IN0k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\r3ljs3kien-i4yb34ulf7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqj3sxw5f0", "Integrity": "VHlY2Fab1aexXczdBkv7TKwHKgvBRP9yQDghKagMd7Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material-lite.css", "FileLength": 502171, "LastWriteTime": "2025-07-10T11:22:50.0857172+00:00"}, "FSESW8aOG+gDJF7MEjBmhtpcrFTSiGeQ9AW+hLTp0/Q=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\31pjcyl7b8-w1t5l0bda4.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6tcxpndh46", "Integrity": "5rsQAuCOhQT11re2U+6BvfBSOZbmUnQfG36BRvrS4vU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material3.css", "FileLength": 452721, "LastWriteTime": "2025-07-10T11:22:50.2052589+00:00"}, "QJWAYrZRM2A8hjcJrgK+Apkt6MpdfI1ozekpGCbQsA4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6k3aze592a-1fw3u0jo2i.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gyeay29ld7", "Integrity": "OOR/F7cfOatwf5E4zlvMG0ABu4q0qG0Hci2AH06Ck8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material3-dark.css", "FileLength": 452348, "LastWriteTime": "2025-07-10T11:22:50.334136+00:00"}, "FAp2OXWY19UyBKktCeGmlJTr62C8tYDZ+GRdrw3TLA0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6ypvevihw1-gee2jdsvyr.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1thoeg5mbj", "Integrity": "27Fx34ynykXcJMwbIj567oGROwDPAHNFZ50jQzhmUWQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material3-dark-lite.css", "FileLength": 452362, "LastWriteTime": "2025-07-10T11:22:50.5400155+00:00"}, "hLspH7bDCubYK7TUEtTzpkGPxpXaGY0LiIENX5P+CUE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\a06iirivll-bl13kyt8lt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qu5439qykf", "Integrity": "or27DV/vGAtC2cPjGaao1jJyffzjiyGEuSvGzxWYnLM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\material3-lite.css", "FileLength": 452724, "LastWriteTime": "2025-07-10T11:22:50.1113286+00:00"}, "hvECDZ4qVvmFn3W1LTreodYmmgdgOsXIYzyRUXFECuE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6d7zsmg5a9-7pplzasn08.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgfmugf0iv", "Integrity": "P/okEyWjImnAm82+NiKiCoKxP41R6nDmJgiKO5LyDBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind.css", "FileLength": 436122, "LastWriteTime": "2025-07-10T11:22:50.1896138+00:00"}, "K01IM+CrUw3i9wU3vQGTsP0PKyyEv4wWU4b9+n4jsmI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\iy9vurkk9t-q4acqztfpx.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b45jiywnd", "Integrity": "77+CTzP6sRDbDpnCYCxQ1AUTuyGa4KUaMA4QZGI6Wpc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind-dark.css", "FileLength": 438980, "LastWriteTime": "2025-07-10T11:22:50.334136+00:00"}, "+OGEm3/KRBcoktsVZAJXTx46H7bcLxodhjnlkJyPJ5E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\hhus5v12qi-7meyybutq6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "35znexb8bt", "Integrity": "WUzSELEYeAehefPLUQwZNJXt/oL/AQTNqI405AI2QjY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind-dark-lite.css", "FileLength": 438976, "LastWriteTime": "2025-07-10T11:22:50.5239672+00:00"}, "eszA+dsJGt85mD3EIOtArSpogzy4KtyZpsLouNzkgjg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\u360ovkwm8-yj8hpavwps.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qybpjcfmhl", "Integrity": "LuT2zKXKRcbMZqGoIZEjOGPI3KlDkkf67VTrqWg1aow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind-lite.css", "FileLength": 436126, "LastWriteTime": "2025-07-10T11:22:50.6452848+00:00"}, "wAbA2b9L4OuukLupgitOyoLHNkMj2mRftqwI+5JSwvQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\clkons3prr-ipgmxz4e11.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xjnv57c2rm", "Integrity": "A5YKEMSZKkL0/PfqMPyj/ZrBUpz87pzXNN7mUAkJl5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind3.css", "FileLength": 495522, "LastWriteTime": "2025-07-10T11:22:50.7413457+00:00"}, "TrJWtJYnU6LF/mXCSyMXYJKFKDszQOhbqBsAmMMDne8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6zteovpk60-h23sj4fdft.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dfx40h8886", "Integrity": "OeaiTE95obZPNxIhJYNoAXwBXOxytnMmQ+cZFZHFQgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind3-dark.css", "FileLength": 493684, "LastWriteTime": "2025-07-10T11:22:50.8374283+00:00"}, "Y6aLI0OGufU0evWX0FhHhD/Xto+U7n7uSGGhY0YmTnc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\awup78ll1e-rnlp968la7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hfrbv8u11r", "Integrity": "nR7XY09FOJt1MmwkJ80eVY5jKUcg8dvFfoqX2DOBprE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind3-dark-lite.css", "FileLength": 493688, "LastWriteTime": "2025-07-10T11:22:50.9236919+00:00"}, "0MDB3wAPijHp3NiS1QMFJnQMHb9sAQLk+zk2z3K93Bk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\9zslhy3vwt-bkc2k4qqy6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4n4vi1n4z2", "Integrity": "wHBjXRv4sGqNdWZRZrluJBh0OCm34DDhLvrvfRP+vUg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.39\\staticwebassets\\styles\\tailwind3-lite.css", "FileLength": 495527, "LastWriteTime": "2025-07-10T11:22:50.9910837+00:00"}, "DG4eRGiG/qEOdOOb9BpuGexRJ8vR/AUzIuV5j3OIUfY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7c3acd42hn", "Integrity": "mTB1BoNq7t/us6gvhhRKI480CaK7vT9E+t+AL9an8wU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 267, "LastWriteTime": "2025-07-10T11:22:50.99359+00:00"}, "DVcUxzvfJBaa2ml362Sn8SbeDku1nkKLdHIijWTCnGA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0hkgbsy7ft", "Integrity": "pIZWamBcM95RyKOecGwL1QrEdg/IKc5rscB4MFCIujM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 996, "LastWriteTime": "2025-07-10T11:22:50.9956038+00:00"}, "qNxbEwOuPc6pbNZgsvEpGgbMnmbV5+f8D+fSqRvZI7o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h1hghvnkss", "Integrity": "RbdcZZJvJ98rtWAD8Wl13T6Jd/A0WUJIyozbvbTNME8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 315, "LastWriteTime": "2025-07-10T11:22:50.9970454+00:00"}, "3/TnlBycZb+po7wbGd7OC1VXZbx6QkJ09AcMtavpWwg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3qb8k9lguq", "Integrity": "pWpjnsF9tEf741/+XNZJIUp59j8Fqiqzv5qPR9T/F/k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 224, "LastWriteTime": "2025-07-10T11:22:50.551017+00:00"}, "VYuP7h0lroUBFZy2ufmTaEdbIdaUJCk00SvmdAYgFHY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1kw23c60ji", "Integrity": "UQRUr2KMtLUqt9ogdiIWMXFjGLFL3UBvDZCD4pGFuKQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 2941, "LastWriteTime": "2025-07-10T11:22:50.5605413+00:00"}, "uvd4mk0iddSCt9QPBlCWv0Hd3PufkJWNm+oLOASghKo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fyjiiw4wfc", "Integrity": "ebidgEF0qxv0FVWWHIu9oAwtiTaKJTgfFFW7oNulicU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 751, "LastWriteTime": "2025-07-10T11:22:50.5695303+00:00"}, "7PiH4TNE7ya663Tw89fM4h4hVgscIQpUOKcPjQuDawo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1z47admwto", "Integrity": "E+0P+4Q5g9SzSGdE20j3R0nckgPdNoGc0x+TGJ/TIYI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 233, "LastWriteTime": "2025-07-10T11:22:50.5732684+00:00"}, "MERi0FLzhMzRVJeKV+ZR/7IQoYTirnwFhYSF4wevoAg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jk2pvfrq94", "Integrity": "QySeeEbzUPczOjYjev2fIc86i78Pib7zlTPyfCkLv+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 756, "LastWriteTime": "2025-07-10T11:22:50.5807146+00:00"}, "V5a4TtwFo3exs/EL+9HV2YstL4++jrk4CIsoGezM23c=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u6v528bpot", "Integrity": "2oCN2IEWRZPjloAzwScHmDLBlebfEgP366812p/E2c8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 141, "LastWriteTime": "2025-07-10T11:22:50.5975793+00:00"}, "fkj8at/BAs83rNkt1U3Lf+ipue9QQv1mF6hDPUVvAiE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "je4eg9uejw", "Integrity": "y0GyYNSHHrdFmkoHrD7jQi0oehiE/vh7Pi7iAEYeorU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 833, "LastWriteTime": "2025-07-10T11:22:50.599944+00:00"}, "Fumwmp3Bcbfy5mxJ3QSJ8NtnPXwRWDktc7exmC7yXsQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5y418xaf05", "Integrity": "TVs9bq3kylVYMAlXR3Kt4NTTdLquoTIUYrtItz80G30=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 888, "LastWriteTime": "2025-07-10T11:22:50.6025037+00:00"}, "ywv7c009jjNIjEpnLIJpHIYylJb4IyQZVYbLlpqsSUI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5oe6m2x2uv", "Integrity": "xT1lGfZ95NQfS99lKqhohgyvrwO4H0pOZ0PZuIj2+Xo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 253, "LastWriteTime": "2025-07-10T11:22:50.5902598+00:00"}, "VF1xe9E3E8juFDX7eC0XfmQmAV7T4sohjZEKX+EhB+s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2vkc6rmnnw", "Integrity": "YtlDojVlcrK2+2QBRP4LPueVRl8gCsL6Ez4wknxUJLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 571, "LastWriteTime": "2025-07-10T11:22:49.640069+00:00"}, "VbLRNCJrXOTx377oIJ0xPkXluME8Ehw7+8+GA2k+bGY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f8na2y7sdz", "Integrity": "4fE5RW6001fSJc6+Ju/+RSpNSo5ZsvCuJri+aL4wof8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 541, "LastWriteTime": "2025-07-10T11:22:49.6432053+00:00"}, "BUIh3HpONs1K63GpkCCzLcv/Q4HVlbdfZUJZ7JhzDTU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0g1tupjq4p", "Integrity": "kD64FhYDGF1SA7wVn4LL4nX1pTWXmxuMYqo2xXip44E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 137, "LastWriteTime": "2025-07-10T11:22:49.6452049+00:00"}, "Xrfpze7DVEPZ8pvT+A+GbXbzLkIAjhscxBVdnBx3zpc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fn6hmwpiy", "Integrity": "8zrNWZqy7raoxnB2CdCgVMkZDFcG6ue/1K4DaARm2RI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 298, "LastWriteTime": "2025-07-10T11:22:49.6474168+00:00"}, "WJ0qpxZoCZbxdFvWuF+iW8KMBnwU7ln5Iz7kA+lhoNk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zhd8n844ok", "Integrity": "V831rKT4rCpErJth1Z/6nQ2pjce3CEI7KGHIdbWXhx8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 1016, "LastWriteTime": "2025-07-10T11:22:49.6499261+00:00"}, "Z35ql9kbD1L/EI/b8oA3+5wwHL5TK8sQogYkuQWzDuc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4s5d9cwut2", "Integrity": "fMg9R056t4+Y6PyTy3Auu6F5M2p0mm1ICT5RlLGEPQw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 1747, "LastWriteTime": "2025-07-10T11:22:49.6514431+00:00"}, "25/p1mRTTfLdzFFWjxLLDyZh95i3kTBap7Xi3h7Au6k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azo1hjcky2", "Integrity": "Si6lAYTROr2Y2E+yQLNmoIuyol3tTTH2x0JT3bD9qzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 652, "LastWriteTime": "2025-07-10T11:22:49.6536053+00:00"}, "N6wH5VjdzxXLX2COoMa0xbe+qj8cw6vknqCTIwZD85s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rv3cg16bo", "Integrity": "gR+Q2B3kReBjCAmxaX0jrZ+LKn/mCFQH9svQins3Ffg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 1836, "LastWriteTime": "2025-07-10T11:22:49.6556067+00:00"}, "0D6IM4FBqfThwj7978WLBDdSHrV8xysGeM72e/+eK7E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3dn9v1xa0n", "Integrity": "eW7o1Dje91+P1j/ydSB4TyhEVA3pV/cMFp5ETKNpf94=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 334, "LastWriteTime": "2025-07-10T11:22:49.6576057+00:00"}, "2Xs/wjOy3PPIpnjwi1p9SOTGSYPZ4Rh0jAVsOJojB5Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dix5wgjndd", "Integrity": "G3dG1kRqAVy5qnnnPHCI1eiXWJKlPgYyT43usniQ3po=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 285, "LastWriteTime": "2025-07-10T11:22:49.6587297+00:00"}, "f9Izl1eZ88okZp0L25EobvvSZhfAWOCv0PAQOgi+kgU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdpoyavzo1", "Integrity": "ge8qfaevzXuPADoLXJRU/hwb7Kf7ZbPVfpfiy5/WzSE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 223, "LastWriteTime": "2025-07-10T11:22:49.6597314+00:00"}, "91dAZeOJxC5Wpwj5USbxPPYyuVciCdfUAeW/dk/fr/I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhffyjj8te", "Integrity": "KrpRzB4c//N15dQfcbeepiBFyskX0d9lId+TSbEmr0g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 472, "LastWriteTime": "2025-07-10T11:22:49.6622407+00:00"}, "4iGyAr6upjLX3vJUA0HaQ7M9eCiBnUiV6jN7hU/1qOY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6y9yx2mqnc", "Integrity": "IrDP3f1jd+L0Llm1IWUdLxzfiqNeIJ3gein+ePzMnDw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 1340, "LastWriteTime": "2025-07-10T11:22:49.6633817+00:00"}, "tcHQwua8tErU9Lok35HItISxZyMjdCb5yUzhupDkTb8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "razgc08jz5", "Integrity": "TR1S8EwhHXGT0IDFQjqJEelFP9eV7MQuyLQoSh0H3hM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 292, "LastWriteTime": "2025-07-10T11:22:49.6644218+00:00"}, "IFb4b1qNKLIlADNoVn1OdKUXtpQJZxxHdpz+jwdCsSw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n5p97eiurb", "Integrity": "m+bgmT5d1joH0Fxa7CS+eJ+VwQ3mfXBcVitL9BGnJDA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 464, "LastWriteTime": "2025-07-10T11:22:49.6669594+00:00"}, "pV3lLCwEkOPYB8WgJKG9sIVa5XAu//3avEfSaC3QfPQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gzh5leg0or", "Integrity": "tOg3Q58NSmbnSFU3YAHG8lAku8SwpggUpqTf+AnATaA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 511, "LastWriteTime": "2025-07-10T11:22:49.6679613+00:00"}, "O0f3TDPzH4hqu5D3lDEgxIIO4n12QIwMMAXF8342/4U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n43ij1gg6a", "Integrity": "qYgukRZJexj+3utKj3bgZeh/3x4zsF0K/IgLGNtQZlU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 279, "LastWriteTime": "2025-07-10T11:22:49.6699604+00:00"}, "wkqzE1AMhpH5e34PcgedswCYI34J5He5YkLnafcAXkY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lx5bc53mj0", "Integrity": "yWJK/jJRjYMvTfcO31YHBmrdz81VC2EyYHDDmtydagw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 2181, "LastWriteTime": "2025-07-10T11:22:49.6709606+00:00"}, "X04bVsM9ngVEkZu500xCP66PYRBW0fpBQLlgvYMs/bQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mz0b1j7sap", "Integrity": "whbSv+mD/ml9KCw7jXjGBPg1LWnmjRIlmI9T2b4UiTE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 514, "LastWriteTime": "2025-07-10T11:22:49.6729593+00:00"}, "03+N8HYKmAwkAptbImCROo9Uk+aNeMMCSB7e1gjweJM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vs7zrwl0nd", "Integrity": "P92YOWDKjxE0y0TlxQ45fEOnMJLoTqI3ePaSkqMS2GE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 1188, "LastWriteTime": "2025-07-10T11:22:49.674011+00:00"}, "M7bW3NsxsYoM4Qm3d02j6k7f2WWmcYi0cmJOLn+eTCg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhsh70qfxk", "Integrity": "ho7R4iSGK4SldCFlK5wThNxgWCzXFykru4YyKh0WJ5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 90913, "LastWriteTime": "2025-07-10T11:22:49.6870849+00:00"}, "LH7xSfrgBK5sGdpIn23qbaeFRJ+4gb+eBl1ctx8Xt9A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3x8k8dzeqr", "Integrity": "2YmtOo2Bk8vpEVJoJ5uKDm6c1zUB6j043XC0D2H1AD0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 575, "LastWriteTime": "2025-07-10T11:22:49.689084+00:00"}, "pv/7iOidmYqFnJiUQyCa7ObsGfHAfXEwaoiR0CZAyAw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0sncgxxouj", "Integrity": "oLgcOgt815aO1mw0Btae91K0hEcxrB2dJEZkTGZ996A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 278202, "LastWriteTime": "2025-07-10T11:22:49.7478049+00:00"}, "iZiwr/ZBXzecywnS5yyIr2cgeifZWxpJUhuQ62fenVU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\acr8rx8p2c-jluxomxr3m.gz", "SourceId": "GELProgramsInventory", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GELProgramsInventory", "RelativePath": "app#[.{fingerprint=jluxomxr3m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e401qau8jc", "Integrity": "IqdhHuUSUwX1X7vw4ASlKEH9YnP8q0xovmdCbO3Wkgo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\wwwroot\\app.css", "FileLength": 2292, "LastWriteTime": "2025-07-10T11:36:20.2000989+00:00"}, "7juJ8La2Ki0tI933HLyPZ4J+cwj8J62vUMIjSXsvmog=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\mo295nqnms-a8m5cweeeb.gz", "SourceId": "GELProgramsInventory", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GELProgramsInventory", "RelativePath": "favicon#[.{fingerprint=a8m5cweeeb}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lv19d4ri1a", "Integrity": "P6HBbEJ0eZi/m1PmO8SAHdJL1XHEjrq2+bDgcIheHt0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\wwwroot\\favicon.ico", "FileLength": 5481, "LastWriteTime": "2025-07-10T11:22:49.7500158+00:00"}, "YoIyI0GDS6wnPiDmbJxWgplZzVp5KQo5HVKSJ5Fm7zw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\suu3tl2qwh-uhfllo7vmv.gz", "SourceId": "GELProgramsInventory", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GELProgramsInventory", "RelativePath": "GELProgramsInventory.modules.json.gz", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wvxjj87u8r", "Integrity": "tMpc4Mp5Kart9mS3UELWfInMDPFZYORmj0wiAw7JwBs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 95, "LastWriteTime": "2025-07-10T11:22:49.752925+00:00"}, "7sXU6mc3ScYrusfYEP3aJokrN8/5PqogV17Ag7Tjy+Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mcb4qwo5eu", "Integrity": "ilM3cnZ9w1Cds3H/szasZoTjgcOxMI6YHlcwRSDZ87w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 13828, "LastWriteTime": "2025-07-10T11:22:49.7587216+00:00"}, "UTdhH9eP9ELKq+ttcZtLPGz7aC1a0drufCbz95re/dk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\4yq6jrn84a-tnv30r1bl8.gz", "SourceId": "GELProgramsInventory", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/GELProgramsInventory", "RelativePath": "GELProgramsInventory#[.{fingerprint=tnv30r1bl8}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\scopedcss\\bundle\\GELProgramsInventory.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "utdp7ws6s3", "Integrity": "Qjl948eSnEILonn3wELtC6BV9k8NVi6yotYo3mJkPSk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\Debug\\net9.0\\scopedcss\\bundle\\GELProgramsInventory.styles.css", "FileLength": 112, "LastWriteTime": "2025-07-10T11:22:49.7598531+00:00"}}, "CachedCopyCandidates": {}}