namespace GELProgramsInventory.Models.Dtos;

public class ProgramDto
{
    public int ProgramId { get; set; }
    public string? ProgramName { get; set; }
    public int? ClientId { get; set; }
    public string? ClientName { get; set; }
    public int? GenreId { get; set; }
    public string? GenreName { get; set; }
    public int? ProductionHouseId { get; set; }
    public string? ProductionHouseName { get; set; }
    public int? OnAirFormatId { get; set; }
    public string? OnAirFormatName { get; set; }
    public int? SyndicationFormatId { get; set; }
    public string? SyndicationFormatName { get; set; }
    
    public string? DriveSerialNumber { get; set; }
    public bool? IsSyndication { get; set; }
    public string? YearOfLaunch { get; set; }
    public string? Remark { get; set; }
    public bool? IsActive { get; set; }
    public string Status => IsActive == true ? "Active" : "Inactive";
}