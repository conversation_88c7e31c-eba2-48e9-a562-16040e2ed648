@page "/production-houses"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@inject ProductionHouseService ProductionHouseService
@rendermode InteractiveServer

<h1>Production Houses</h1>

<SfButton OnClick="@OpenCreateDialog">Create New Production House</SfButton>
<SfDialog @ref="dlgProductionHouse" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="600px" Visible="false">
    <DialogTemplates>
        <Header>Production House Information</Header>
        <Content>
            <EditForm Model="_selectedProductionHouse" OnValidSubmit="SaveProductionHouse" FormName="ProductionHouseForm">
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Production House Name" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedProductionHouse.ProductionHouseName"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfTextBox Placeholder="Contact Info" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedProductionHouse.ContactInfo"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md" style="display: flex; align-items: center; gap: 5px;" >
                        <span><b>Status</b></span> <SfSwitch @bind-Checked="_selectedProductionHouse.IsActive"></SfSwitch> <span> @_selectedProductionHouse.Status</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfButton CssClass="e-primary">Save</SfButton>
                        
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfGrid @ref="_grid" DataSource="@_productionHousesList" AllowPaging="true">
    <GridColumns>
        
        <GridColumn Field="@nameof(ProductionHouseDto.ProductionHouseName)" HeaderText="Production House Name" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProductionHouseDto.ContactInfo)" HeaderText="Contact Info" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProductionHouseDto.Status)" HeaderText="Status" AutoFit="true" ></GridColumn>
        
        <GridColumn HeaderText="Actions" Width="150">
            <Template Context="productionHouseContext">
                <SfButton OnClick="@(() => OpenEditDialog((ProductionHouseDto)productionHouseContext))">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteProductionHouse(((ProductionHouseDto)productionHouseContext).ProductionHouseId))">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private List<ProductionHouseDto> _productionHousesList = new();
    private SfGrid<ProductionHouseDto> _grid = new();
    private ProductionHouseDto _selectedProductionHouse = new();
    private SfDialog? dlgProductionHouse;
    private List<Breadcrumb.BreadcrumbItem> _breadcrumbItems = new List<Breadcrumb.BreadcrumbItem>
    {
        new Breadcrumb.BreadcrumbItem { Text = "Home", Href = "/" },
        new Breadcrumb.BreadcrumbItem { Text = "Production Houses", Href = "/productionhouses", IsActive = true }
    };
    protected override async Task OnInitializedAsync()
    {
        _productionHousesList = await ProductionHouseService.GetProductionHousesAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedProductionHouse = new ProductionHouseDto() {IsActive = true};
        await dlgProductionHouse.ShowAsync();
    }

    private async Task OpenEditDialog(ProductionHouseDto productionHouse)
    {
        _selectedProductionHouse = productionHouse;
        await dlgProductionHouse.ShowAsync();
    }

    private async Task SaveProductionHouse()
    {
        if (_selectedProductionHouse.ProductionHouseId == 0)
        {
            await ProductionHouseService.CreateProductionHouseAsync(_selectedProductionHouse);
        }
        else
        {
            await ProductionHouseService.UpdateProductionHouseAsync(_selectedProductionHouse);
        }

        _productionHousesList = await ProductionHouseService.GetProductionHousesAsync();
        await _grid.Refresh();
        await dlgProductionHouse.HideAsync();
    }

    private async Task DeleteProductionHouse(int productionHouseId)
    {
        await ProductionHouseService.DeleteProductionHouseAsync(productionHouseId);
        _productionHousesList = await ProductionHouseService.GetProductionHousesAsync();
        _grid.Refresh();
    }

}