{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\GELProgramsInventory.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\GELProgramsInventory.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\GELProgramsInventory.csproj", "projectName": "GELProgramsInventory", "projectPath": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\GELProgramsInventory.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\GELProgramsInventory\\GELProgramsInventory\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.FluentUI.AspNetCore.Components": {"target": "Package", "version": "[4.11.5, )"}, "Microsoft.FluentUI.AspNetCore.Components.Icons": {"target": "Package", "version": "[4.11.5, )"}, "microsoft.entityframeworkcore": {"target": "Package", "version": "[9.0.7, )"}, "microsoft.entityframeworkcore.design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "microsoft.entityframeworkcore.sqlserver": {"target": "Package", "version": "[9.0.7, )"}, "microsoft.entityframeworkcore.tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "syncfusion.blazor": {"target": "Package", "version": "[30.1.39, )"}, "syncfusion.blazor.themes": {"target": "Package", "version": "[30.1.39, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}