using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class ProductionHouseService
{
    private readonly ApplicationDbContext _context;

    public ProductionHouseService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<ProductionHouseDto>> GetProductionHousesAsync()
    {
        return await _context.ProductionHouses
            .Select(ph => new ProductionHouseDto
            {
                ProductionHouseId = ph.ProductionHouseId,
                ProductionHouseName = ph.ProductionHouseName,
                ContactInfo = ph.ContactInfo,
                IsActive = ph.IsActive,
                
            })
            .ToListAsync();
    }

    public async Task<ProductionHouseDto?> GetProductionHouseByIdAsync(int id)
    {
        var productionHouse = await _context.ProductionHouses.FindAsync(id);
        if (productionHouse == null)
        {
            return null;
        }

        return new ProductionHouseDto
        {
            ProductionHouseId = productionHouse.ProductionHouseId,
            ProductionHouseName = productionHouse.ProductionHouseName,
            ContactInfo = productionHouse.ContactInfo,
            IsActive = productionHouse.IsActive,
            
        };
    }

    public async Task<ProductionHouse> CreateProductionHouseAsync(ProductionHouseDto productionHouseDto)
    {
        var productionHouse = new ProductionHouse
        {
            ProductionHouseName = productionHouseDto.ProductionHouseName,
            ContactInfo = productionHouseDto.ContactInfo,
            IsActive = productionHouseDto.IsActive,
            CreatedDate = DateTime.Now
        };

        _context.ProductionHouses.Add(productionHouse);
        await _context.SaveChangesAsync();
        return productionHouse;
    }

    public async Task UpdateProductionHouseAsync(ProductionHouseDto productionHouseDto)
    {
        var productionHouse = await _context.ProductionHouses.FindAsync(productionHouseDto.ProductionHouseId);
        if (productionHouse != null)
        {
            productionHouse.ProductionHouseName = productionHouseDto.ProductionHouseName;
            productionHouse.ContactInfo = productionHouseDto.ContactInfo;
            productionHouse.IsActive = productionHouseDto.IsActive;
            await _context.SaveChangesAsync();
        }
    }

    public async Task DeleteProductionHouseAsync(int id)
    {
        var productionHouse = await _context.ProductionHouses.FindAsync(id);
        if (productionHouse != null)
        {
            _context.ProductionHouses.Remove(productionHouse);
            await _context.SaveChangesAsync();
        }
    }
}