﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Models;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Client> Clients { get; set; }

    public virtual DbSet<Genre> Genres { get; set; }

    public virtual DbSet<OnAirFormat> OnAirFormats { get; set; }

    public virtual DbSet<ProductionHouse> ProductionHouses { get; set; }

    public virtual DbSet<Program> Programs { get; set; }

    public virtual DbSet<SyndicationFormat> SyndicationFormats { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Client>(entity =>
        {
            entity.HasKey(e => e.ClientId).HasName("PK__Client__E67E1A04B08897F7");

            entity.ToTable("Client");

            entity.HasIndex(e => e.ClientName, "UQ__Client__65800DA011486FEE").IsUnique();

            entity.Property(e => e.ClientId).HasColumnName("ClientID");
            entity.Property(e => e.ClientName)
                .IsRequired()
                .HasMaxLength(200);
            entity.Property(e => e.ContactInfo).HasMaxLength(500);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<Genre>(entity =>
        {
            entity.HasKey(e => e.GenreId).HasName("PK__Genre__0385055E9EF0B29D");

            entity.ToTable("Genre");

            entity.HasIndex(e => e.GenreName, "UQ__Genre__BBE1C3392B4921E8").IsUnique();

            entity.Property(e => e.GenreId).HasColumnName("GenreID");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.GenreName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<OnAirFormat>(entity =>
        {
            entity.HasKey(e => e.OnAirFormatId).HasName("PK__OnAirFor__1BDA66A07DF0470B");

            entity.ToTable("OnAirFormat");

            entity.HasIndex(e => e.FormatName, "UQ__OnAirFor__CE1A172465ED59FD").IsUnique();

            entity.Property(e => e.OnAirFormatId).HasColumnName("OnAirFormatID");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FormatName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<ProductionHouse>(entity =>
        {
            entity.HasKey(e => e.ProductionHouseId).HasName("PK__Producti__132ACF74DE83883B");

            entity.ToTable("ProductionHouse");

            entity.HasIndex(e => e.ProductionHouseName, "UQ__Producti__A9D8F3944DE5FDF4").IsUnique();

            entity.Property(e => e.ProductionHouseId).HasColumnName("ProductionHouseID");
            entity.Property(e => e.ContactInfo).HasMaxLength(500);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ProductionHouseName)
                .IsRequired()
                .HasMaxLength(200);
        });

        modelBuilder.Entity<Program>(entity =>
        {
            entity.HasKey(e => e.ProgramId).HasName("PK__Programs__75256038305830E3");

            entity.HasIndex(e => e.DriveSerialNumber, "IX_Programs_DriveSerialNumber");

            entity.HasIndex(e => e.GenreId, "IX_Programs_GenreID");

            entity.HasIndex(e => e.IsSyndication, "IX_Programs_IsSyndication");

            entity.HasIndex(e => e.ProgramName, "IX_Programs_ProgramName");

            entity.HasIndex(e => e.YearOfLaunch, "IX_Programs_YearOfLaunch");

            entity.Property(e => e.ProgramId).HasColumnName("ProgramID");
            entity.Property(e => e.ClientId).HasColumnName("ClientID");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.DriveSerialNumber).HasMaxLength(100);
            entity.Property(e => e.GenreId).HasColumnName("GenreID");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsSyndication).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.OnAirFormatId).HasColumnName("OnAirFormatID");
            entity.Property(e => e.ProductionHouseId).HasColumnName("ProductionHouseID");
            entity.Property(e => e.ProgramName)
                .IsRequired()
                .HasMaxLength(300);
            entity.Property(e => e.SyndicationFormatId).HasColumnName("SyndicationFormatID");
            entity.Property(e => e.YearOfLaunch).HasMaxLength(4);

            entity.HasOne(d => d.Client).WithMany(p => p.Programs)
                .HasForeignKey(d => d.ClientId)
                .HasConstraintName("FK_Programs_Client");

            entity.HasOne(d => d.Genre).WithMany(p => p.Programs)
                .HasForeignKey(d => d.GenreId)
                .HasConstraintName("FK_Programs_Genre");

            entity.HasOne(d => d.OnAirFormat).WithMany(p => p.Programs)
                .HasForeignKey(d => d.OnAirFormatId)
                .HasConstraintName("FK_Programs_OnAirFormat");

            entity.HasOne(d => d.ProductionHouse).WithMany(p => p.Programs)
                .HasForeignKey(d => d.ProductionHouseId)
                .HasConstraintName("FK_Programs_ProductionHouse");

            entity.HasOne(d => d.SyndicationFormat).WithMany(p => p.Programs)
                .HasForeignKey(d => d.SyndicationFormatId)
                .HasConstraintName("FK_Programs_SyndicationFormat");
        });

        modelBuilder.Entity<SyndicationFormat>(entity =>
        {
            entity.HasKey(e => e.SyndicationFormatId).HasName("PK__Syndicat__A95ADF9A82276EFE");

            entity.ToTable("SyndicationFormat");

            entity.HasIndex(e => e.FormatName, "UQ__Syndicat__CE1A1724D1B0F5BB").IsUnique();

            entity.Property(e => e.SyndicationFormatId).HasColumnName("SyndicationFormatID");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FormatName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}