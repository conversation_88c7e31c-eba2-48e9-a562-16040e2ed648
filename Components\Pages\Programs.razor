@page "/programs"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using GELProgramsInventory.Models
@inject ProgramService ProgramService
@inject ClientService ClientService
@inject GenreService GenreService
@inject ProductionHouseService ProductionHouseService
@inject OnAirFormatService OnAirFormatService
@inject SyndicationFormatService SyndicationFormatService
@rendermode InteractiveServer

<h1>Programs</h1>

<SfButton OnClick="@OpenCreateDialog">Create New Program</SfButton>
<SfDialog @ref="dlgProgram" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="800px" Visible="false">
    <DialogTemplates>
        <Header>Program Information</Header>
        <Content>
            <EditForm Model="_selectedProgram" OnValidSubmit="SaveProgram" FormName="ProgramForm">
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Program Name" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedProgram.ProgramName"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfDropDownList TValue="int?" TItem="ClientDto" Placeholder="Select Client" FloatLabelType="FloatLabelType.Always"
                                        DataSource="_clients" @bind-Value="_selectedProgram.ClientId">
                            <DropDownListFieldSettings Text="ClientName" Value="ClientId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfDropDownList TValue="int?" TItem="Genre" Placeholder="Select Genre" FloatLabelType="FloatLabelType.Always"
                                        DataSource="_genres" @bind-Value="_selectedProgram.GenreId">
                            <DropDownListFieldSettings Text="GenreName" Value="GenreId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfDropDownList TValue="int?" TItem="ProductionHouseDto" Placeholder="Select Production House" FloatLabelType="FloatLabelType.Always"
                                        DataSource="_productionHouses" @bind-Value="_selectedProgram.ProductionHouseId">
                            <DropDownListFieldSettings Text="ProductionHouseName" Value="ProductionHouseId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfDropDownList TValue="int?" TItem="OnAirFormat" Placeholder="Select On Air Format" FloatLabelType="FloatLabelType.Always"
                                        DataSource="_onAirFormats" @bind-Value="_selectedProgram.OnAirFormatId">
                            <DropDownListFieldSettings Text="OnAirFormatName" Value="OnAirFormatId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfDropDownList TValue="int?" TItem="SyndicationFormat" Placeholder="Select Syndication Format" FloatLabelType="FloatLabelType.Always"
                                        DataSource="_syndicationFormats" @bind-Value="_selectedProgram.SyndicationFormatId">
                            <DropDownListFieldSettings Text="SyndicationFormatName" Value="SyndicationFormatId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Year of Launch" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedProgram.YearOfLaunch"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Drive Serial Number" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedProgram.DriveSerialNumber"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Remark" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedProgram.Remark" Multiline="true"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2" style="display: flex; align-items: center; gap: 5px;">
                        <span><b>Is Syndication</b></span> <SfSwitch @bind-Checked="_selectedProgram.IsSyndication"></SfSwitch> <span>@(_selectedProgram.IsSyndication == true ? "Yes" : "No")</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md" style="display: flex; align-items: center; gap: 5px;">
                        <span><b>Status</b></span> <SfSwitch @bind-Checked="_selectedProgram.IsActive"></SfSwitch> <span>@_selectedProgram.Status</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfButton CssClass="e-primary">Save</SfButton>

                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfGrid @ref="_grid" DataSource="@_programsList" AllowPaging="true">
    <GridColumns>

        <GridColumn Field="@nameof(ProgramDto.ProgramName)" HeaderText="Program Name" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProgramDto.ClientName)" HeaderText="Client" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProgramDto.GenreName)" HeaderText="Genre" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProgramDto.ProductionHouseName)" HeaderText="Production House" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProgramDto.OnAirFormatName)" HeaderText="On Air Format" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProgramDto.SyndicationFormatName)" HeaderText="Syndication Format" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProgramDto.YearOfLaunch)" HeaderText="Year of Launch" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProgramDto.Status)" HeaderText="Status" AutoFit="true"></GridColumn>

        <GridColumn HeaderText="Actions" Width="150">
            <Template Context="programContext">
                <SfButton OnClick="@(() => OpenEditDialog((ProgramDto)programContext))">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteProgram(((ProgramDto)programContext).ProgramId))">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private List<ProgramDto> _programsList = new();
    private SfGrid<ProgramDto> _grid = new();
    private ProgramDto _selectedProgram = new();
    private SfDialog? dlgProgram;
    private List<ClientDto> _clients = new();
    private List<Genre> _genres = new();
    private List<ProductionHouseDto> _productionHouses = new();
    private List<OnAirFormat> _onAirFormats = new();
    private List<SyndicationFormat> _syndicationFormats = new();
    private List<Breadcrumb.BreadcrumbItem> _breadcrumbItems = new List<Breadcrumb.BreadcrumbItem>
    {
        new Breadcrumb.BreadcrumbItem { Text = "Home", Href = "/" },
        new Breadcrumb.BreadcrumbItem { Text = "Programs", Href = "/programs", IsActive = true }
    };

    protected override async Task OnInitializedAsync()
    {
        _programsList = await ProgramService.GetProgramsAsync();
        _clients = await ClientService.GetClientsAsync();
        _genres = await GenreService.GetGenresAsync();
        _productionHouses = await ProductionHouseService.GetProductionHousesAsync();
        _onAirFormats = await OnAirFormatService.GetOnAirFormatsAsync();
        _syndicationFormats = await SyndicationFormatService.GetSyndicationFormatsAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedProgram = new ProgramDto() { IsActive = true };
        await dlgProgram!.ShowAsync();
    }

    private async Task OpenEditDialog(ProgramDto program)
    {
        _selectedProgram = program;
        await dlgProgram!.ShowAsync();
    }

    private async Task SaveProgram()
    {
        if (_selectedProgram.ProgramId == 0)
        {
            await ProgramService.CreateProgramAsync(_selectedProgram);
        }
        else
        {
            await ProgramService.UpdateProgramAsync(_selectedProgram);
        }

        _programsList = await ProgramService.GetProgramsAsync();
        await _grid.Refresh();
        await dlgProgram!.HideAsync();
    }

    private async Task DeleteProgram(int programId)
    {
        await ProgramService.DeleteProgramAsync(programId);
        _programsList = await ProgramService.GetProgramsAsync();
        await _grid.Refresh();
    }

}
