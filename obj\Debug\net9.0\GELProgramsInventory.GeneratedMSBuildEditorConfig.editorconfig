is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = GELProgramsInventory
build_property.RootNamespace = GELProgramsInventory
build_property.ProjectDir = C:\Users\<USER>\source\repos\GELProgramsInventory\GELProgramsInventory\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\source\repos\GELProgramsInventory\GELProgramsInventory
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Breadcrumb.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xCcmVhZGNydW1iLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Layout/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTmF2TWVudS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Pages/ClientDialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDbGllbnREaWFsb2cucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Pages/Clients.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDbGllbnRzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Pages/Counter.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDb3VudGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Pages/ProductionHouses.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xQcm9kdWN0aW9uSG91c2VzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Pages/Programs.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xQcm9ncmFtcy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Pages/Weather.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXZWF0aGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/GELProgramsInventory/GELProgramsInventory/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 
