using GELProgramsInventory.Models;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class SyndicationFormatService
{
    private readonly ApplicationDbContext _context;

    public SyndicationFormatService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<SyndicationFormat>> GetSyndicationFormatsAsync()
    {
        return await _context.SyndicationFormats.ToListAsync();
    }
}
